#!/usr/bin/env python3
"""
测试超时检测功能
使用慢速控件分析器来触发超时重启
"""

import sys
import os
import time
import subprocess
import signal
from pathlib import Path

def test_timeout_with_monitor():
    """测试使用监控器的超时检测"""
    print("🧪 测试控件识别超时监控器")
    print("=" * 60)
    
    scripts_dir = Path(__file__).parent / 'scripts'
    
    # 使用慢速控件分析器
    cmd = [
        'python3', str(scripts_dir / 'start_auto_recording_with_lifecycle.py'),
        '--enable-widget-monitor',  # 启用控件识别监控器
        '--widget-timeout', '3.0',  # 3秒超时
        '--debug',
        '--duration', '30'  # 运行30秒
    ]
    
    print(f"🚀 启动命令: {' '.join(cmd)}")
    print(f"📊 预期：当控件识别超过3秒时，监控器会杀死并重启进程")
    print(f"⏰ 测试将运行30秒...")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ 进程已启动，PID: {process.pid}")
        
        # 等待30秒或进程结束
        start_time = time.time()
        while time.time() - start_time < 30:
            if process.poll() is not None:
                print(f"⚠️ 进程已退出，返回码: {process.returncode}")
                break
            
            time.sleep(1)
            elapsed = time.time() - start_time
            if int(elapsed) % 5 == 0:
                print(f"⏱️ 测试进行中... {elapsed:.0f}/30秒")
        
        # 停止进程
        if process.poll() is None:
            print(f"🛑 停止进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        # 读取输出
        stdout, stderr = process.communicate()
        
        print(f"\n📊 测试结果:")
        print(f"   返回码: {process.returncode}")
        
        if stderr:
            print(f"\n📝 标准错误输出:")
            stderr_lines = stderr.strip().split('\n')
            
            # 查找重要的日志信息
            restart_count = 0
            timeout_count = 0
            
            for line in stderr_lines:
                if '🚨 控件识别超时' in line:
                    timeout_count += 1
                    print(f"   🚨 发现超时: {line}")
                elif '🔄 触发进程重启' in line or '重启完成' in line:
                    restart_count += 1
                    print(f"   🔄 发现重启: {line}")
                elif 'PID:' in line and '启动' in line:
                    print(f"   🚀 进程启动: {line}")
            
            print(f"\n📈 统计信息:")
            print(f"   检测到超时次数: {timeout_count}")
            print(f"   检测到重启次数: {restart_count}")
            
            if timeout_count > 0 and restart_count > 0:
                print(f"✅ 超时检测和重启功能正常工作！")
            else:
                print(f"❌ 超时检测或重启功能可能有问题")
                
            # 显示最后20行日志
            print(f"\n📝 最后20行日志:")
            for line in stderr_lines[-20:]:
                print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_direct_monitor():
    """直接测试监控器"""
    print("\n🧪 直接测试控件识别监控器")
    print("=" * 60)
    
    scripts_dir = Path(__file__).parent / 'scripts'
    
    # 直接使用监控器监控慢速分析器
    cmd = [
        'python3', str(scripts_dir / 'widget_recognition_monitor.py'),
        '--timeout', '3.0',
        '--debug',
        str(scripts_dir / 'test_slow_widget_analyzer.py'),
        '--delay', '5',  # 5秒延迟，肯定会超时
        '--duration', '20'
    ]
    
    print(f"🚀 启动命令: {' '.join(cmd)}")
    print(f"📊 预期：5秒延迟的控件识别会在3秒后被强制重启")
    print(f"⏰ 测试将运行20秒...")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ 监控器进程已启动，PID: {process.pid}")
        
        # 等待20秒或进程结束
        start_time = time.time()
        while time.time() - start_time < 20:
            if process.poll() is not None:
                print(f"⚠️ 监控器进程已退出，返回码: {process.returncode}")
                break
            
            time.sleep(1)
            elapsed = time.time() - start_time
            if int(elapsed) % 3 == 0:
                print(f"⏱️ 测试进行中... {elapsed:.0f}/20秒")
        
        # 停止进程
        if process.poll() is None:
            print(f"🛑 停止监控器进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        # 读取输出
        stdout, stderr = process.communicate()
        
        print(f"\n📊 测试结果:")
        print(f"   返回码: {process.returncode}")
        
        if stderr:
            print(f"\n📝 标准错误输出:")
            stderr_lines = stderr.strip().split('\n')
            
            # 查找重要的日志信息
            restart_count = 0
            timeout_count = 0
            
            for line in stderr_lines:
                if '🚨 控件识别超时' in line:
                    timeout_count += 1
                    print(f"   🚨 发现超时: {line}")
                elif '🔄 重启目标进程' in line or '重启完成' in line:
                    restart_count += 1
                    print(f"   🔄 发现重启: {line}")
                elif 'PID:' in line and ('启动' in line or '已启动' in line):
                    print(f"   🚀 进程启动: {line}")
            
            print(f"\n📈 统计信息:")
            print(f"   检测到超时次数: {timeout_count}")
            print(f"   检测到重启次数: {restart_count}")
            
            if timeout_count > 0 and restart_count > 0:
                print(f"✅ 监控器超时检测和重启功能正常工作！")
            else:
                print(f"❌ 监控器功能可能有问题")
                
            # 显示最后15行日志
            print(f"\n📝 最后15行日志:")
            for line in stderr_lines[-15:]:
                print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 控件识别超时检测测试套件")
    print("=" * 80)
    
    # 信号处理
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 测试1: 直接测试监控器
        print("📍 测试1: 直接测试监控器功能")
        success1 = test_direct_monitor()
        
        # 等待一下
        time.sleep(2)
        
        # 测试2: 通过生命周期管理器测试
        print("\n📍 测试2: 通过生命周期管理器测试")
        success2 = test_timeout_with_monitor()
        
        print(f"\n✅ 测试完成")
        print(f"📊 测试结果:")
        print(f"   直接监控器测试: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"   生命周期管理器测试: {'✅ 成功' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print(f"\n🎉 所有测试通过！超时检测功能正常工作")
        else:
            print(f"\n⚠️ 部分测试失败，请检查配置和日志")
            
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试套件异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
