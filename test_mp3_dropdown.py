#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试mp3下拉框识别问题的脚本
"""

import sys
import os
import time
sys.path.append('/home/<USER>/kylin-robot-ide/scripts')

from UNI import UNI
import pyats<PERSON>

def analyze_mp3_dropdown_issue(x=389, y=425):
    """分析mp3下拉框识别问题"""
    print(f"=" * 80)
    print(f"分析坐标({x}, {y})的mp3下拉框识别问题")
    print(f"=" * 80)
    
    uni = UNI()
    
    try:
        # 1. 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if not active_window:
            print("❌ 无法获取活动窗口")
            return
        
        print(f"✅ 活动窗口: {getattr(active_window, 'name', 'unnamed')}")
        print(f"   进程ID: {processid}")
        print(f"   窗口角色: {windowRoleName}")
        print()
        
        # 2. 获取应用程序对象
        app = uni._get_app_from_window(active_window)
        if not app:
            print("❌ 无法获取应用程序对象")
            return
        
        print(f"✅ 应用程序: {getattr(app, 'name', 'unnamed')}")
        print()
        
        # 3. 查找所有combo box控件
        print("🔍 查找所有combo box控件:")
        combo_boxes = []
        find_all_combo_boxes(app, combo_boxes)
        
        for i, combo in enumerate(combo_boxes):
            combo_name = getattr(combo, 'name', 'unnamed')
            print(f"  [{i}] {combo_name}")
            
            # 检查是否是mp3相关的下拉框
            if 'mp3' in combo_name.lower() or 'format' in combo_name.lower() or 'type' in combo_name.lower():
                print(f"      ⭐ 可能是mp3相关的下拉框")
        print()
        
        # 4. 查找展开的下拉框
        print("🔍 查找展开的下拉框:")
        expanded_dropdowns = []
        uni._find_expanded_dropdowns(app, expanded_dropdowns)
        
        print(f"找到 {len(expanded_dropdowns)} 个展开的下拉框:")
        for i, dropdown in enumerate(expanded_dropdowns):
            dropdown_name = getattr(dropdown, 'name', 'unnamed')
            print(f"  [{i}] {dropdown_name}")
            
            # 详细分析每个展开的下拉框
            analyze_dropdown_options(dropdown, x, y, uni)
        print()
        
        # 5. 直接在目标坐标查找控件
        print("🎯 直接在目标坐标查找控件:")
        uni.childEle = None
        uni._find_accessible_at_point(active_window, x, y, activewindow_region)
        
        if uni.childEle:
            element_name = getattr(uni.childEle, 'name', 'unnamed')
            element_role = uni.childEle.getRoleName() if hasattr(uni.childEle, 'getRoleName') else 'unknown'
            print(f"  直接找到: {element_name} ({element_role})")
            
            # 检查是否是list item
            if 'list item' in element_role.lower():
                print(f"  ✅ 这是一个list item！")
                analyze_list_item_parent(uni.childEle)
            else:
                print(f"  ❌ 不是list item，而是: {element_role}")
        else:
            print("  ❌ 直接查找未找到控件")
        print()
        
        # 6. 测试修复后的识别逻辑
        print("🔧 测试完整的识别逻辑:")
        result, msg = uni.kdk_getElement_Uni(x, y)
        
        if isinstance(result, dict):
            print(f"  识别结果: {result.get('Name', 'N/A')} ({result.get('Rolename', 'N/A')})")
            print(f"  识别状态: {result.get('capture_status', 'N/A')}")
            
            if result.get('Rolename') == 'list item':
                print("  ✅ 成功识别为list item！")
            else:
                print(f"  ❌ 识别为其他控件: {result.get('Rolename', 'N/A')}")
        else:
            print(f"  ❌ 识别失败: {msg}")
        
    except Exception as e:
        print(f"❌ 分析过程发生异常: {e}")
        import traceback
        traceback.print_exc()

def find_all_combo_boxes(element, combo_list, depth=0, max_depth=10):
    """递归查找所有combo box控件"""
    if depth > max_depth:
        return
    
    try:
        role = element.getRoleName().lower()
        if 'combo' in role:
            combo_list.append(element)
        
        if hasattr(element, 'childCount'):
            for i in range(min(element.childCount, 50)):
                try:
                    child = element.getChildAtIndex(i)
                    find_all_combo_boxes(child, combo_list, depth + 1, max_depth)
                except:
                    continue
    except:
        pass

def analyze_dropdown_options(dropdown, target_x, target_y, uni):
    """详细分析下拉框选项"""
    dropdown_name = getattr(dropdown, 'name', 'unnamed')
    print(f"    📋 分析下拉框: {dropdown_name}")
    
    # 收集选项
    options = []
    uni._collect_dropdown_options(dropdown, options)
    
    print(f"    选项数量: {len(options)}")
    
    for j, option in enumerate(options):
        option_name = getattr(option, 'name', 'unnamed')
        option_role = option.getRoleName() if hasattr(option, 'getRoleName') else 'unknown'
        
        try:
            component = option.queryComponent()
            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            
            # 检查是否包含目标坐标
            contains_target = (extents.x <= target_x <= extents.x + extents.width and
                             extents.y <= target_y <= extents.y + extents.height)
            
            contains_mark = " ⭐ 包含目标坐标!" if contains_target else ""
            
            print(f"      [{j}] {option_name} ({option_role})")
            print(f"          坐标: ({extents.x}, {extents.y}) 大小: {extents.width}x{extents.height}{contains_mark}")
            
            if contains_target:
                print(f"          🎯 这个选项包含目标坐标({target_x}, {target_y})！")
                
        except Exception as e:
            print(f"      [{j}] {option_name} ({option_role}) - 坐标获取失败: {e}")

def analyze_list_item_parent(list_item):
    """分析list item的父级关系"""
    print(f"    🔍 分析list item的父级关系:")
    
    try:
        # 检查父元素
        if hasattr(list_item, 'parent') and list_item.parent:
            parent = list_item.parent
            parent_name = getattr(parent, 'name', 'unnamed')
            parent_role = parent.getRoleName() if hasattr(parent, 'getRoleName') else 'unknown'
            print(f"      父元素: {parent_name} ({parent_role})")
            
            # 检查祖父元素
            if hasattr(parent, 'parent') and parent.parent:
                grandparent = parent.parent
                grandparent_name = getattr(grandparent, 'name', 'unnamed')
                grandparent_role = grandparent.getRoleName() if hasattr(grandparent, 'getRoleName') else 'unknown'
                print(f"      祖父元素: {grandparent_name} ({grandparent_role})")
                
                if 'combo' in grandparent_role.lower():
                    print(f"      ✅ 确认这是combo box的选项！")
                    
                    # 检查combo box是否被识别为展开
                    uni_temp = UNI()
                    is_expanded = uni_temp._check_if_combo_box_expanded(grandparent)
                    print(f"      展开状态检测: {'✅ 展开' if is_expanded else '❌ 未展开'}")
        
    except Exception as e:
        print(f"      ❌ 分析父级关系失败: {e}")

if __name__ == "__main__":
    print("mp3下拉框识别问题分析工具")
    print("请确保mp3下拉框已经展开，然后运行分析...")
    
    # 可以修改这里的坐标
    x = int(input("请输入X坐标 (默认389): ") or "389")
    y = int(input("请输入Y坐标 (默认425): ") or "425")
    
    analyze_mp3_dropdown_issue(x, y)
