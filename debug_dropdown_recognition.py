#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试坐标(431, 568)处下拉菜单项识别问题的脚本
"""

import sys
import os
import time

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from UNI import UNI
except ImportError as e:
    print(f"❌ 无法导入UNI模块: {e}")
    sys.exit(1)

def debug_dropdown_recognition(x, y):
    """调试指定坐标的下拉菜单项识别"""
    print(f"🔍 开始调试坐标({x}, {y})处的下拉菜单项识别问题")
    print("=" * 80)
    
    # 创建UNI实例
    uni = UNI()
    
    print(f"📍 目标坐标: ({x}, {y})")
    print(f"🖥️  显示服务器类型: {uni.display_server}")
    print()
    
    # 1. 获取活动窗口信息
    print("🔍 步骤1: 获取活动窗口信息")
    try:
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        if active_window:
            print(f"✅ 找到活动窗口:")
            print(f"   窗口名称: {active_window.name}")
            print(f"   进程ID: {processid}")
            print(f"   窗口区域: {activewindow_region}")
            print(f"   窗口角色: {windowRoleName}")
            print(f"   子控件数量: {windowChildCount}")
        else:
            print("❌ 未找到活动窗口")
            return
    except Exception as e:
        print(f"❌ 获取活动窗口失败: {e}")
        return
    
    print()
    
    # 2. 检查是否会被跳过
    print("🔍 步骤2: 检查是否会被跳过控件识别")
    try:
        should_skip = uni._should_skip_control_detection(x, y)
        if should_skip:
            print("⚠️  该坐标会被跳过控件识别（可能导致卡顿）")
            return
        else:
            print("✅ 该坐标不会被跳过")
    except Exception as e:
        print(f"⚠️  检查跳过逻辑失败: {e}")
    
    print()
    
    # 3. 尝试X11层级检测
    print("🔍 步骤3: 尝试X11层级检测")
    try:
        topmost_element = uni._find_topmost_element_at_point(x, y)
        if topmost_element == "SKIP_UNKNOWN_WINDOW":
            print("⚠️  X11层级检测返回跳过Unknown窗口")
        elif topmost_element:
            print(f"✅ X11层级检测找到控件:")
            print(f"   控件名称: {getattr(topmost_element, 'name', 'N/A')}")
            print(f"   控件角色: {getattr(topmost_element, 'getRoleName', lambda: 'N/A')()}")
        else:
            print("❌ X11层级检测未找到控件")
    except Exception as e:
        print(f"❌ X11层级检测失败: {e}")
    
    print()
    
    # 4. 尝试智能深度搜索
    print("🔍 步骤4: 尝试智能深度搜索")
    try:
        found_element = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
        if found_element:
            print(f"✅ 智能深度搜索找到控件:")
            print(f"   控件名称: {getattr(found_element, 'name', 'N/A')}")
            print(f"   控件角色: {getattr(found_element, 'getRoleName', lambda: 'N/A')()}")
        else:
            print("❌ 智能深度搜索未找到控件")
    except Exception as e:
        print(f"❌ 智能深度搜索失败: {e}")
    
    print()
    
    # 5. 检查下拉框选项识别
    print("🔍 步骤5: 检查下拉框选项识别")
    try:
        # 先找到一个基础控件
        data, text_info = uni.kdk_getElement_Uni(x, y)
        if data and "error" not in data:
            print(f"✅ 基础识别成功:")
            print(f"   控件名称: {data.get('Name', 'N/A')}")
            print(f"   控件角色: {data.get('Rolename', 'N/A')}")
            
            # 检查是否是下拉框相关控件
            role = data.get('Rolename', '').lower()
            if 'list' in role or 'combo' in role or 'menu' in role:
                print(f"   🎯 识别为下拉框相关控件: {role}")
            else:
                print(f"   ⚠️  不是下拉框相关控件: {role}")
                
        else:
            print(f"❌ 基础识别失败:")
            print(f"   错误信息: {data.get('error', '未知错误') if data else '无返回数据'}")
    except Exception as e:
        print(f"❌ 基础识别异常: {e}")
    
    print()
    
    # 6. 专门检查下拉框选项
    print("🔍 步骤6: 专门检查下拉框选项")
    try:
        dropdown_option = uni._find_expanded_dropdown_option(active_window, x, y)
        if dropdown_option:
            print(f"✅ 找到下拉框选项:")
            print(f"   选项名称: {getattr(dropdown_option, 'name', 'N/A')}")
            print(f"   选项角色: {getattr(dropdown_option, 'getRoleName', lambda: 'N/A')()}")
        else:
            print("❌ 未找到下拉框选项")
    except Exception as e:
        print(f"❌ 下拉框选项检查失败: {e}")
    
    print()
    print("🔍 调试完成")
    print("=" * 80)

def check_dropdown_detection_logic():
    """检查下拉框检测逻辑"""
    print("🔍 检查下拉框检测逻辑")
    print("=" * 80)
    
    uni = UNI()
    
    # 检查下拉框检测的关键参数
    print("📋 下拉框检测关键参数:")
    
    # 检查距离阈值
    print("   - 距离阈值设置:")
    print("     * 最大允许距离: 100px (硬编码)")
    print("     * 如果选项距离目标坐标超过100px，会被拒绝")
    
    # 检查下拉框状态检测
    print("   - 下拉框状态检测:")
    print("     * 需要检测combo box是否真正展开")
    print("     * 展开的下拉框才会进行选项匹配")
    
    # 检查选项匹配逻辑
    print("   - 选项匹配逻辑:")
    print("     * 通过距离计算找到最近的选项")
    print("     * 只有距离在阈值内的选项才会被返回")

if __name__ == "__main__":
    # 调试目标坐标
    target_x, target_y = 431, 568
    
    print("🚀 下拉菜单项识别问题调试工具")
    print(f"📍 目标坐标: ({target_x}, {target_y})")
    print()
    
    # 详细调试
    debug_dropdown_recognition(target_x, target_y)
    
    print()
    
    # 检查下拉框检测逻辑
    check_dropdown_detection_logic()
    
    print()
    print("💡 可能的问题原因:")
    print("1. 下拉框没有真正展开，导致选项不可见")
    print("2. 目标坐标距离最近的下拉框选项超过100px")
    print("3. 下拉框选项的控件结构发生了变化")
    print("4. X11层级检测失败，回退到智能搜索但没找到正确控件")
    print("5. 下拉框选项被其他控件遮挡")
    print()
    print("🔧 建议的解决方案:")
    print("1. 检查下拉框是否真正展开")
    print("2. 调整距离阈值或选项匹配逻辑")
    print("3. 优化智能深度搜索的下拉框选项检测")
    print("4. 检查控件层级结构是否发生变化")
