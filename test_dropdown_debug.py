#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下拉框List Item识别调试测试脚本
用于排查下拉框识别不稳定的问题
"""

import sys
import os
import time
import json
sys.path.append('/home/<USER>/kylin-robot-ide/scripts')

from UNI import UNI
import pyatspi

class DropdownDebugger:
    def __init__(self):
        self.uni = UNI()
        self.debug_log = []
    
    def log_debug(self, message):
        """记录调试信息"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        self.debug_log.append(log_entry)
        print(log_entry, file=sys.stderr)
    
    def test_coordinate_recognition(self, x, y, test_name=""):
        """测试指定坐标的控件识别"""
        self.log_debug(f"=" * 60)
        self.log_debug(f"开始测试: {test_name}")
        self.log_debug(f"目标坐标: ({x}, {y})")
        
        start_time = time.time()
        
        try:
            # 执行识别
            result, msg = self.uni.kdk_getElement_Uni(x, y)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            self.log_debug(f"识别完成，耗时: {execution_time:.3f}s")
            self.log_debug(f"返回消息: {msg}")
            
            if isinstance(result, dict):
                self.log_debug("识别结果:")
                for key, value in result.items():
                    self.log_debug(f"  {key}: {value}")
            else:
                self.log_debug(f"识别结果类型异常: {type(result)}")
            
            return result, msg, execution_time
            
        except Exception as e:
            self.log_debug(f"识别过程发生异常: {e}")
            import traceback
            traceback.print_exc(file=sys.stderr)
            return None, str(e), 0
    
    def analyze_dropdown_structure(self, x, y):
        """分析指定坐标周围的下拉框结构"""
        self.log_debug(f"=" * 60)
        self.log_debug(f"分析坐标({x}, {y})周围的下拉框结构")
        
        try:
            # 获取活动窗口
            active_window, processid, activewindow_region, windowRoleName, windowChildCount = self.uni._get_active_window2(x, y)
            
            if not active_window:
                self.log_debug("无法获取活动窗口")
                return
            
            self.log_debug(f"活动窗口: {getattr(active_window, 'name', 'unnamed')}")
            self.log_debug(f"进程ID: {processid}")
            self.log_debug(f"窗口角色: {windowRoleName}")
            
            # 获取应用程序对象
            app = self.uni._get_app_from_window(active_window)
            if not app:
                self.log_debug("无法获取应用程序对象")
                return
            
            self.log_debug(f"应用程序: {getattr(app, 'name', 'unnamed')}")
            
            # 查找展开的下拉框
            expanded_dropdowns = []
            self.uni._find_expanded_dropdowns(app, expanded_dropdowns)
            
            self.log_debug(f"找到 {len(expanded_dropdowns)} 个展开的下拉框:")
            
            for i, dropdown in enumerate(expanded_dropdowns):
                dropdown_name = getattr(dropdown, 'name', 'unnamed')
                dropdown_role = dropdown.getRoleName() if hasattr(dropdown, 'getRoleName') else 'unknown'
                self.log_debug(f"  [{i}] {dropdown_name} ({dropdown_role})")
                
                # 收集选项
                options = []
                self.uni._collect_dropdown_options(dropdown, options)
                self.log_debug(f"      选项数量: {len(options)}")
                
                for j, option in enumerate(options):
                    option_name = getattr(option, 'name', 'unnamed')
                    option_role = option.getRoleName() if hasattr(option, 'getRoleName') else 'unknown'
                    
                    try:
                        component = option.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        coords_info = f"({extents.x}, {extents.y}) {extents.width}x{extents.height}"
                        
                        # 检查是否包含目标坐标
                        contains_target = (extents.x <= x <= extents.x + extents.width and
                                         extents.y <= y <= extents.y + extents.height)
                        contains_mark = " ★" if contains_target else ""
                        
                    except:
                        coords_info = "坐标获取失败"
                        contains_mark = ""
                    
                    self.log_debug(f"        [{j}] {option_name} ({option_role}) {coords_info}{contains_mark}")
        
        except Exception as e:
            self.log_debug(f"分析过程发生异常: {e}")
            import traceback
            traceback.print_exc(file=sys.stderr)
    
    def test_dropdown_detection_stability(self, x, y, test_count=5):
        """测试下拉框识别的稳定性"""
        self.log_debug(f"=" * 60)
        self.log_debug(f"开始稳定性测试: 坐标({x}, {y}), 测试次数: {test_count}")
        
        results = []
        
        for i in range(test_count):
            self.log_debug(f"第 {i+1} 次测试:")
            result, msg, exec_time = self.test_coordinate_recognition(x, y, f"稳定性测试 {i+1}")
            
            results.append({
                'test_id': i+1,
                'result': result,
                'message': msg,
                'execution_time': exec_time
            })
            
            # 短暂延迟
            time.sleep(0.1)
        
        # 分析结果
        self.log_debug(f"=" * 60)
        self.log_debug("稳定性测试结果分析:")
        
        success_count = 0
        error_count = 0
        different_results = set()
        
        for result in results:
            if result['result'] and isinstance(result['result'], dict):
                if result['result'].get('capture_status') == 'success':
                    success_count += 1
                    # 记录不同的识别结果
                    key_info = (
                        result['result'].get('Name', ''),
                        result['result'].get('Rolename', ''),
                        result['result'].get('Key', '')
                    )
                    different_results.add(key_info)
                else:
                    error_count += 1
            else:
                error_count += 1
        
        self.log_debug(f"成功次数: {success_count}/{test_count}")
        self.log_debug(f"失败次数: {error_count}/{test_count}")
        self.log_debug(f"不同结果数量: {len(different_results)}")
        
        if len(different_results) > 1:
            self.log_debug("检测到不一致的结果:")
            for i, result_info in enumerate(different_results):
                self.log_debug(f"  结果{i+1}: Name='{result_info[0]}', Role='{result_info[1]}', Key='{result_info[2]}'")
        
        avg_time = sum(r['execution_time'] for r in results) / len(results)
        self.log_debug(f"平均执行时间: {avg_time:.3f}s")
        
        return results
    
    def save_debug_log(self, filename=None):
        """保存调试日志到文件"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"dropdown_debug_{timestamp}.log"
        
        with open(filename, 'w', encoding='utf-8') as f:
            for log_entry in self.debug_log:
                f.write(log_entry + '\n')
        
        print(f"调试日志已保存到: {filename}")
        return filename

def main():
    debugger = DropdownDebugger()
    
    print("下拉框List Item识别调试工具")
    print("=" * 50)
    print("请选择测试模式:")
    print("1. 单次坐标识别测试")
    print("2. 下拉框结构分析")
    print("3. 稳定性测试")
    print("4. 综合测试")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice in ['1', '2', '3', '4']:
        x = int(input("请输入X坐标: "))
        y = int(input("请输入Y坐标: "))
        
        if choice == '1':
            debugger.test_coordinate_recognition(x, y, "单次识别测试")
        elif choice == '2':
            debugger.analyze_dropdown_structure(x, y)
        elif choice == '3':
            count = int(input("请输入测试次数 (默认5): ") or "5")
            debugger.test_dropdown_detection_stability(x, y, count)
        elif choice == '4':
            debugger.test_coordinate_recognition(x, y, "综合测试-单次识别")
            debugger.analyze_dropdown_structure(x, y)
            debugger.test_dropdown_detection_stability(x, y, 3)
    else:
        print("无效选择")
        return
    
    # 保存日志
    log_file = debugger.save_debug_log()
    print(f"\n调试完成，详细日志请查看: {log_file}")

if __name__ == "__main__":
    main()
