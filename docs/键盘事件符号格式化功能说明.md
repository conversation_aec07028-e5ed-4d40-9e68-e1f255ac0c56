# 键盘事件符号格式化功能说明

## 概述

在全流程录制中，为了确保键盘事件生成的 Action YAML 格式正确，我们增强了键盘事件的格式化功能。现在不仅数字会自动添加引号，符号字符也会自动添加引号，确保 YAML 格式的正确性和一致性。

## 功能特性

### 🔢 数字格式化
- **整数**: `1`, `23`, `456` → `"1"`, `"23"`, `"456"`
- **小数**: `3.14`, `7.89` → `"3.14"`, `"7.89"`
- **零**: `0` → `"0"`

### 🔣 符号格式化
- **标点符号**: `!`, `@`, `#`, `$`, `%` → `"!"`, `"@"`, `"#"`, `"$"`, `"%"`
- **运算符**: `+`, `-`, `*`, `/`, `=` → `"+"`, `"-"`, `"*"`, `"/"`, `"="`
- **括号**: `(`, `)`, `[`, `]`, `{`, `}` → `"("`, `")"`, `"["`, `"]"`, `"{"`, `"}"`
- **其他符号**: `:`, `;`, `'`, `"`, `,`, `.`, `<`, `>`, `?` → 对应的引号格式

### 🔤 保持原样的字符
- **字母**: `a`, `A`, `z`, `Z` → 保持原样
- **功能键**: `enter`, `space`, `tab`, `ctrl`, `alt`, `shift` → 保持原样
- **组合键**: `ctrl+c`, `alt+f4`, `shift+1` → 保持原样
- **混合内容**: `abc123`, `123abc` → 保持原样

## 技术实现

### 核心方法

#### formatKeyValue()
```typescript
private formatKeyValue(keyValue: string): string {
    // 检查是否是纯数字（包括小数）
    if (this.isNumericString(keyValue)) {
        return `"${keyValue}"`;
    }

    // 检查是否是纯数字组合（如 "123", "456"）
    if (/^\d+$/.test(keyValue)) {
        return `"${keyValue}"`;
    }

    // 检查是否是符号字符
    if (this.isSymbolString(keyValue)) {
        return `"${keyValue}"`;
    }

    // 其他情况保持原样
    return keyValue;
}
```

#### isNumericString()
```typescript
private isNumericString(str: string): boolean {
    // 检查是否是纯数字字符串
    return /^\d+(\.\d+)?$/.test(str);
}
```

#### isSymbolString()
```typescript
private isSymbolString(str: string): boolean {
    // 常见的符号字符，包括标点符号、运算符、特殊字符等
    const symbolPattern = /^[!@#$%^&*()_+\-=\[\]{}|;':",./<>?`~\\]+$/;
    return symbolPattern.test(str);
}
```

### 支持的符号列表

```
! @ # $ % ^ & * ( ) _ + - = [ ] { } | ; ' " : , . / < > ? ` ~ \
```

## YAML 输出示例

### 数字按键
```yaml
# 2025-07-29T14:28:39.906Z - key_press
- action: kyrobot_type
  driver: default
  kwargs:
    type: OCR
    key: "1"
```

### 符号按键
```yaml
# 2025-07-29T14:28:39.907Z - key_press
- action: kyrobot_type
  driver: default
  kwargs:
    type: OCR
    key: "!"
```

### 字母按键
```yaml
# 2025-07-29T14:28:39.907Z - key_press
- action: kyrobot_type
  driver: default
  kwargs:
    type: OCR
    key: a
```

### 功能按键
```yaml
# 2025-07-29T14:28:39.907Z - key_press
- action: kyrobot_type
  driver: default
  kwargs:
    type: OCR
    key: enter
```

## 使用场景

### 1. 密码输入
当用户输入包含符号的密码时，符号会被正确格式化：
```yaml
- action: kyrobot_type
  kwargs:
    key: "@"  # @ 符号被正确引用
```

### 2. 数学表达式
输入数学运算符时：
```yaml
- action: kyrobot_type
  kwargs:
    key: "+"  # 加号被正确引用
```

### 3. 编程代码
输入代码时的各种符号：
```yaml
- action: kyrobot_type
  kwargs:
    key: "{"  # 大括号被正确引用
```

## 兼容性

### 向后兼容
- 现有的数字格式化功能保持不变
- 字母和功能键的处理方式保持不变
- 不影响现有的测试用例

### 新增功能
- 符号字符现在会自动添加引号
- 提高了 YAML 格式的正确性
- 减少了手动修正的需要

## 测试验证

项目包含完整的测试用例 `test_symbol_formatting.js`，验证：
- ✅ 数字格式化正确
- ✅ 符号格式化正确
- ✅ 字母保持原样
- ✅ 功能键保持原样
- ✅ 混合内容保持原样
- ✅ YAML 生成正确

## 注意事项

1. **符号识别**: 只有纯符号字符会被添加引号，混合内容保持原样
2. **YAML 兼容性**: 确保生成的 YAML 符合标准格式
3. **性能影响**: 格式化过程对性能影响微乎其微
4. **扩展性**: 可以通过修改正则表达式来支持更多符号

## 相关文件

- `src/vs/workbench/contrib/gat/browser/features/actionYamlGenerator.ts` - 主要实现
- `test_symbol_formatting.js` - 测试用例
- `docs/键盘事件录制功能实现.md` - 相关文档
