# 外部进程监控控件识别超时重启功能说明

## 📋 功能概述

为了解决控件识别过程中可能出现的长时间阻塞问题，我们实现了**外部进程监控控件识别超时重启机制**。该机制通过独立的监控进程监听控件识别的开始/结束信号，当超过3秒没有收到结束信号时，立即杀死并重启`auto_recording_manager.py`进程。

## 🎯 核心优势

- **外部监控**：监控器运行在独立进程中，不受目标进程阻塞影响
- **精确超时**：基于实际的控件识别开始/结束信号进行计时
- **强制重启**：直接杀死阻塞的进程并重启，确保快速恢复
- **进程间通信**：使用UDP socket进行高效的信号传递
- **完全隔离**：监控逻辑与被监控逻辑完全分离

## 🔧 架构设计

### 1. 组件架构

```
┌─────────────────────────────────────┐
│  start_auto_recording_with_lifecycle │  ← 启动器
│  (主控制进程)                        │
└─────────────┬───────────────────────┘
              │
              ▼
┌─────────────────────────────────────┐
│  widget_recognition_monitor.py      │  ← 监控器进程
│  (外部监控进程)                     │
└─────────────┬───────────────────────┘
              │ 监控
              ▼
┌─────────────────────────────────────┐
│  auto_recording_manager.py          │  ← 被监控进程
│  (录制管理器进程)                   │
└─────────────────────────────────────┘
```

### 2. 通信机制

```
auto_recording_manager.py          widget_recognition_monitor.py
        │                                    │
        │ 控件识别开始                        │
        ├─────── UDP Signal ─────────────────▶│ 启动3秒计时器
        │                                    │
        │ 控件识别进行中...                   │ 计时器运行中...
        │                                    │
        │ 控件识别结束                        │
        ├─────── UDP Signal ─────────────────▶│ 取消计时器
        │                                    │
        │ (如果超时)                          │
        │◀────── SIGTERM/SIGKILL ────────────┤ 杀死进程
        │                                    │
        │ (重启)                              │
        │◀────── subprocess.Popen ───────────┤ 启动新进程
```

## 📡 信号协议

### 控件识别开始信号
```json
{
    "session_id": "abc12345",
    "signal_type": "recognition_start",
    "timestamp": 1642345678.123,
    "data": {
        "x": 400,
        "y": 300,
        "recognition_type": "hover"
    }
}
```

### 控件识别结束信号
```json
{
    "session_id": "abc12345",
    "signal_type": "recognition_end",
    "timestamp": 1642345679.456,
    "data": {
        "x": 400,
        "y": 300,
        "duration": 1.333,
        "success": true,
        "recognition_type": "hover"
    }
}
```

## 🚀 使用方法

### 1. 启用外部监控模式

```bash
# 使用新的外部监控模式
python3 scripts/start_auto_recording_with_lifecycle.py \
    --enable-widget-monitor \
    --widget-timeout 3.0 \
    --debug
```

### 2. 直接使用监控器

```bash
# 直接使用监控器监控auto_recording_manager.py
python3 scripts/widget_recognition_monitor.py \
    --timeout 3.0 \
    --debug \
    scripts/auto_recording_manager.py \
    --debug --json-output
```

### 3. 参数说明

- `--enable-widget-monitor`: 启用控件识别监控器模式
- `--widget-timeout`: 控件识别超时阈值（秒），默认3.0
- `--debug`: 启用调试模式，显示详细日志

## 🔍 监控流程

### 1. 正常流程
```
1. 鼠标悬停触发控件识别
2. auto_recording_manager发送"recognition_start"信号
3. 监控器启动3秒计时器
4. 控件识别在2秒内完成
5. auto_recording_manager发送"recognition_end"信号
6. 监控器取消计时器
7. 继续正常运行
```

### 2. 超时重启流程
```
1. 鼠标悬停触发控件识别
2. auto_recording_manager发送"recognition_start"信号
3. 监控器启动3秒计时器
4. 控件识别阻塞，超过3秒未完成
5. 监控器计时器触发
6. 监控器杀死auto_recording_manager进程
7. 监控器重启auto_recording_manager进程
8. 系统恢复正常运行
```

## 📊 监控日志

### 正常识别日志
```
[MONITOR] 🎯 控件识别监控器已启动
[DEBUG] 收到信号: recognition_start, 会话: abc12345
[DEBUG] 🎯 控件识别开始: 会话=abc12345, 位置=(400, 300)
[DEBUG] ✅ 控件识别结束: 会话=abc12345, 耗时=1.456秒, 成功=true
```

### 超时重启日志
```
[MONITOR] 🚨 控件识别超时!
[MONITOR] 🚨 会话: abc12345
[MONITOR] 🚨 位置: (400, 300)
[MONITOR] 🚨 类型: hover
[MONITOR] 🚨 耗时: 3.001秒 (阈值: 3.0秒)
[MONITOR] 🔄 触发进程重启...
[MONITOR] 🛑 停止目标进程，PID: 12345
[MONITOR] ✅ 目标进程已停止
[MONITOR] 🚀 启动目标进程: python3 scripts/auto_recording_manager.py --debug
[MONITOR] ✅ 目标进程已启动，PID: 12346
[MONITOR] ✅ 目标进程重启完成 (第1次)
```

## 🧪 测试验证

### 运行测试套件
```bash
python3 test_widget_monitor.py
```

### 测试内容
1. **通信机制测试**：验证UDP信号传递
2. **监控器功能测试**：验证超时检测和进程重启
3. **集成测试**：验证与生命周期管理器的集成

## ⚙️ 配置参数

### 监控器参数
- `timeout_threshold`: 超时阈值（秒），默认3.0
- `listen_port`: 监听端口，默认12345
- `debug`: 调试模式开关

### 通信参数
- `session_id`: 会话标识符，用于区分不同的录制会话
- `signal_type`: 信号类型（recognition_start/recognition_end/heartbeat）
- `timestamp`: 信号时间戳

## 🔧 故障排除

### 常见问题

1. **端口占用**
   ```
   [ERROR] 启动监控器失败: [Errno 98] Address already in use
   ```
   解决：检查端口12345是否被占用，或修改监听端口

2. **进程启动失败**
   ```
   [ERROR] 启动目标进程失败: [Errno 2] No such file or directory
   ```
   解决：检查auto_recording_manager.py路径是否正确

3. **信号丢失**
   ```
   [MONITOR] 🚨 控件识别超时! (但实际已完成)
   ```
   解决：检查UDP通信是否正常，防火墙设置

### 调试技巧

1. **启用详细日志**
   ```bash
   --debug
   ```

2. **检查进程状态**
   ```bash
   ps aux | grep auto_recording_manager
   ```

3. **监控网络通信**
   ```bash
   netstat -ulnp | grep 12345
   ```

## 📈 性能对比

| 机制 | 检测延迟 | 重启时间 | 可靠性 | 资源占用 |
|------|----------|----------|--------|----------|
| 内部超时检测 | 高（受阻塞影响） | 慢（等待完成） | 低 | 低 |
| 外部进程监控 | 低（独立计时） | 快（强制杀死） | 高 | 中 |

## 🎯 最佳实践

1. **推荐使用外部监控模式**：更可靠的超时检测
2. **合理设置超时阈值**：根据系统性能调整（建议3-5秒）
3. **启用调试模式**：便于问题排查和性能分析
4. **定期检查日志**：监控重启频率和原因

---

**注意**: 此功能是对原有内部超时检测的重大改进，通过外部进程监控实现了更可靠的超时检测和进程重启机制。
