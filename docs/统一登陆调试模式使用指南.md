# 统一登陆调试模式使用指南

## 概述

为了方便开发和调试，我们为统一登陆功能添加了调试模式。启用调试模式后，可以跳过真实的统一认证登录流程，直接使用模拟用户信息进行登录。

## 功能特性

- **跳过统一认证**：启用调试模式后，不会向统一认证服务器发送请求
- **模拟用户信息**：自动生成模拟的用户信息，包括用户名、姓名和令牌
- **保持功能完整性**：除了跳过认证步骤外，其他登录相关功能保持不变
- **配置持久化**：调试模式设置会保存到配置文件中

## 使用方法

### 方法一：通过命令面板

1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (macOS) 打开命令面板
2. 输入 "调试模式登录" 并选择该命令
3. 确认启用调试模式
4. 输入调试用户名（可选，默认为 "debug_user"）
5. 点击"调试登录"完成登录

### 方法二：通过配置文件

1. 打开设置 (`Ctrl+,` 或 `Cmd+,`)
2. 搜索 "auth.customLogin.debugMode"
3. 勾选该选项以启用调试模式
4. 通过账户菜单进行登录，系统会自动使用调试模式

### 方法三：通过 settings.json

在用户或工作区的 `settings.json` 文件中添加：

```json
{
  "auth.customLogin.debugMode": true
}
```

## 配置选项

### auth.customLogin.debugMode

- **类型**: boolean
- **默认值**: false
- **描述**: 启用后将跳过统一认证登录，使用模拟用户信息

### auth.customLogin.loginUrl

- **类型**: string
- **默认值**: "http://***********:8000/login"
- **描述**: 统一认证登录校验接口地址（调试模式下不使用）

## 调试模式下的用户信息

当启用调试模式时，系统会生成以下格式的模拟用户信息：

```javascript
{
  name: "调试用户 (输入的用户名)",
  username: "输入的用户名或debug_user",
  token: "debug_token_时间戳",
  loginTime: 当前时间戳
}
```

## 禁用调试模式

### 通过命令面板

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "禁用调试模式" 并选择该命令
3. 确认禁用调试模式
4. 系统会自动登出当前用户并禁用调试模式

### 通过设置

1. 打开设置
2. 搜索 "auth.customLogin.debugMode"
3. 取消勾选该选项
4. 手动登出当前用户

## 注意事项

1. **仅用于开发调试**：调试模式仅应在开发和测试环境中使用，不要在生产环境中启用
2. **安全性**：调试模式下的令牌是模拟的，不具备真实的认证能力
3. **功能限制**：某些需要真实用户权限的功能可能在调试模式下无法正常工作
4. **配置作用域**：调试模式配置可以设置为用户级别或工作区级别

## 故障排除

### 调试模式无法启用

1. 检查配置是否正确设置
2. 重启应用程序
3. 查看开发者工具的控制台输出

### 登录后功能异常

1. 确认相关功能是否支持调试模式
2. 检查日志输出中的错误信息
3. 尝试禁用调试模式并使用真实登录

## 开发者信息

### 相关文件

- `src/vs/platform/authentication/common/customAuthenticationService.ts` - 认证服务实现
- `src/vs/workbench/contrib/authentication/browser/actions/debugLoginAction.ts` - 调试登录动作
- `src/vs/workbench/contrib/authentication/browser/authentication.contribution.ts` - 认证贡献和配置

### 上下文键

- `customLoginStatus` - 用户登录状态
- `debugModeEnabled` - 调试模式启用状态

### 配置注册

调试模式配置在 `authentication.contribution.ts` 中注册，属于 GAT 配置分类。
