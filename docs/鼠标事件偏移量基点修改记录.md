# 鼠标事件偏移量基点修改记录

## 概述

本文档记录了将全流程录制中鼠标事件的偏移量基点从控件中心点改为控件左上角坐标的完整修改过程。

## 修改原因

用户需求：将全流程录制中记录的鼠标事件的偏移量由中心点作为基点改成以控件的左上角坐标作为基点来进行实现。

## 修改范围

### 🔧 核心算法修改

#### 1. 偏移量计算方法 `_calculate_relative_offset_percentage`

**修改前（基于中心点）**：
```python
# 计算控件中心点
center_x = widget_x + widget_width / 2
center_y = widget_y + widget_height / 2

# 计算相对偏移（点击位置 - 中心点）
offset_x = click_x - center_x
offset_y = click_y - center_y

# 计算偏移百分比（相对于控件尺寸）
# 正值表示向右/向下偏移，负值表示向左/向上偏移
offset_x_percent = (offset_x / widget_width) * 100
offset_y_percent = (offset_y / widget_height) * 100
```

**修改后（基于左上角）**：
```python
# 计算相对偏移（点击位置 - 左上角坐标）
offset_x = click_x - widget_x
offset_y = click_y - widget_y

# 计算偏移百分比（相对于控件尺寸）
# 0%表示左上角，100%表示右下角
offset_x_percent = (offset_x / widget_width) * 100
offset_y_percent = (offset_y / widget_height) * 100
```

#### 2. 距离计算修改

**修改前**：
```python
# 计算距离中心点的距离百分比
distance = (offset_x ** 2 + offset_y ** 2) ** 0.5
max_distance = ((widget_width / 2) ** 2 + (widget_height / 2) ** 2) ** 0.5
distance_from_center_percent = (distance / max_distance) * 100
```

**修改后**：
```python
# 计算距离左上角的距离百分比
distance = (offset_x ** 2 + offset_y ** 2) ** 0.5
max_distance = (widget_width ** 2 + widget_height ** 2) ** 0.5
distance_from_topleft_percent = (distance / max_distance) * 100
```

### 📝 字段名称修改

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `distance_from_center_percent` | `distance_from_topleft_percent` | 距离计算基点字段名 |
| "距中心" | "距左上角" | 调试输出和注释中的描述 |
| "相对于控件中心" | "相对于控件左上角" | 字段注释说明 |

### 🗂️ 修改的文件

#### 1. Python后端文件
- **`scripts/auto_recording_manager.py`**
  - 修改了两个 `_calculate_relative_offset_percentage` 方法
  - 更新了 `MouseEvent` 数据结构字段注释
  - 更新了所有调用处的字段名引用
  - 更新了调试输出信息

#### 2. TypeScript前端文件
- **`src/vs/workbench/contrib/gat/browser/features/actionYamlGenerator.ts`**
  - 更新了YAML生成器中的字段名引用
  - 修改了注释中的描述文本

#### 3. 测试文件
- **`scripts/test_topleft_offset.py`** (新增)
  - 专门测试基于左上角的偏移量计算逻辑
  - 包含多种测试场景和边界情况

## 修改效果对比

### 📊 偏移量计算对比

以一个200x100的控件为例（左上角坐标100,50）：

| 点击位置 | 修改前（基于中心点） | 修改后（基于左上角） |
|----------|---------------------|---------------------|
| 左上角(100,50) | 偏移(-50%, -50%) | 偏移(0%, 0%) |
| 中心点(200,100) | 偏移(0%, 0%) | 偏移(50%, 50%) |
| 右下角(300,150) | 偏移(50%, 50%) | 偏移(100%, 100%) |

### 🎯 优势分析

#### 修改前（中心点基点）的问题：
- 偏移量可能为负值，不够直观
- 需要额外计算中心点坐标
- 与常见的坐标系统（如CSS）不一致

#### 修改后（左上角基点）的优势：
- ✅ **直观性**：0%表示左上角，100%表示右下角
- ✅ **一致性**：与CSS、图形界面等标准坐标系统一致
- ✅ **简洁性**：直接使用控件原始坐标，无需计算中心点
- ✅ **正值范围**：偏移量始终为正值（控件内点击）

## 测试验证

### ✅ 测试结果

运行 `scripts/test_topleft_offset.py` 的测试结果：

```
📍 测试1: 左上角点击 - ✅ 通过
📍 测试2: 中心点击 - ✅ 通过  
📍 测试3: 右下角点击 - ✅ 通过
📍 测试4: 控件外点击 - ✅ 通过
📍 测试5: 实际控件场景模拟 - ✅ 通过
📍 测试6: 边界值测试 - ✅ 通过
```

### 🔍 关键测试案例

1. **左上角点击**：偏移(0%, 0%)，距左上角0%
2. **中心点击**：偏移(50%, 50%)，距左上角50%
3. **右下角点击**：偏移(100%, 100%)，距左上角100%
4. **控件外点击**：偏移>100%，正常处理

## 兼容性影响

### 🔄 向后兼容性

- **数据结构变化**：字段名从 `distance_from_center_percent` 改为 `distance_from_topleft_percent`
- **计算逻辑变化**：偏移量数值含义完全改变
- **YAML输出变化**：生成的注释文本从"距中心"改为"距左上角"

### ⚠️ 注意事项

1. **历史数据**：之前录制的数据中的偏移量数值不再适用
2. **测试用例**：需要更新相关的测试用例和期望值
3. **文档更新**：需要更新相关的用户文档和API文档

## 使用示例

### 新的偏移量含义

```yaml
# 生成的YAML示例
- action: kyrobot_click
  driver: test_app
  kwargs:
    key: 确定按钮
    type: UNI
    offset_x: 25.0    # 距左边界25%
    offset_y: 33.3    # 距上边界33.3%
    # 点击偏移: X=25.0%, Y=33.3%, 距左上角26.2%
```

### 实际应用场景

- **按钮点击**：可以精确指定点击按钮的特定位置
- **控件定位**：更符合前端开发的坐标思维
- **测试重现**：偏移量更加直观和可预测

## 总结

本次修改成功将鼠标事件偏移量的基点从控件中心点改为控件左上角，实现了：

1. ✅ **算法优化**：简化了计算逻辑，提高了直观性
2. ✅ **标准化**：与主流坐标系统保持一致
3. ✅ **测试验证**：通过了全面的功能测试
4. ✅ **文档完善**：提供了详细的修改记录和使用说明

这一修改使得GAT全流程录制系统的鼠标事件偏移量计算更加直观和标准化，为用户提供了更好的使用体验。
