#!/usr/bin/env python3
"""
测试控件识别监控器功能
验证外部进程监控和超时重启机制
"""

import sys
import os
import time
import threading
import subprocess
import signal
from pathlib import Path

# 添加scripts目录到路径
scripts_dir = Path(__file__).parent / 'scripts'
sys.path.insert(0, str(scripts_dir))

def test_widget_monitor():
    """测试控件识别监控器"""
    print("🧪 测试控件识别监控器功能")
    print("=" * 60)
    
    # 测试参数
    monitor_script = scripts_dir / 'widget_recognition_monitor.py'
    target_script = scripts_dir / 'auto_recording_manager.py'
    
    if not monitor_script.exists():
        print(f"❌ 监控器脚本不存在: {monitor_script}")
        return False
    
    if not target_script.exists():
        print(f"❌ 目标脚本不存在: {target_script}")
        return False
    
    print(f"📍 监控器脚本: {monitor_script}")
    print(f"📍 目标脚本: {target_script}")
    print(f"📍 超时阈值: 3秒")
    
    # 构建命令
    cmd = [
        'python3', str(monitor_script),
        '--timeout', '3.0',
        '--debug',
        str(target_script),
        '--debug',
        '--json-output'
    ]
    
    print(f"\n🚀 启动命令: {' '.join(cmd)}")
    print(f"⏰ 测试将运行30秒，观察监控器行为...")
    
    try:
        # 启动监控器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(scripts_dir.parent)
        )
        
        print(f"✅ 监控器进程已启动，PID: {process.pid}")
        
        # 等待30秒
        start_time = time.time()
        while time.time() - start_time < 30:
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"⚠️ 监控器进程已退出，返回码: {process.returncode}")
                break
            
            time.sleep(1)
            elapsed = time.time() - start_time
            if int(elapsed) % 5 == 0:  # 每5秒报告一次
                print(f"⏱️ 测试进行中... {elapsed:.0f}/30秒")
        
        # 停止进程
        if process.poll() is None:
            print(f"🛑 停止监控器进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"⚡ 强制杀死监控器进程")
                process.kill()
                process.wait()
        
        # 读取输出
        stdout, stderr = process.communicate()
        
        print(f"\n📊 测试结果:")
        print(f"   返回码: {process.returncode}")
        print(f"   标准输出长度: {len(stdout)} 字符")
        print(f"   标准错误长度: {len(stderr)} 字符")
        
        if stderr:
            print(f"\n📝 标准错误输出 (最后20行):")
            stderr_lines = stderr.strip().split('\n')
            for line in stderr_lines[-20:]:
                print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_lifecycle_with_widget_monitor():
    """测试带控件识别监控器的生命周期管理器"""
    print("\n🧪 测试生命周期管理器 + 控件识别监控器")
    print("=" * 60)
    
    # 测试参数
    lifecycle_script = scripts_dir / 'start_auto_recording_with_lifecycle.py'
    
    if not lifecycle_script.exists():
        print(f"❌ 生命周期脚本不存在: {lifecycle_script}")
        return False
    
    print(f"📍 生命周期脚本: {lifecycle_script}")
    
    # 构建命令
    cmd = [
        'python3', str(lifecycle_script),
        '--enable-widget-monitor',  # 启用控件识别监控器
        '--widget-timeout', '3.0',
        '--debug',
        '--json-output'
    ]
    
    print(f"\n🚀 启动命令: {' '.join(cmd)}")
    print(f"⏰ 测试将运行30秒，观察生命周期管理器行为...")
    
    try:
        # 启动生命周期管理器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(scripts_dir.parent)
        )
        
        print(f"✅ 生命周期管理器进程已启动，PID: {process.pid}")
        
        # 等待30秒
        start_time = time.time()
        while time.time() - start_time < 30:
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"⚠️ 生命周期管理器进程已退出，返回码: {process.returncode}")
                break
            
            time.sleep(1)
            elapsed = time.time() - start_time
            if int(elapsed) % 5 == 0:  # 每5秒报告一次
                print(f"⏱️ 测试进行中... {elapsed:.0f}/30秒")
        
        # 停止进程
        if process.poll() is None:
            print(f"🛑 停止生命周期管理器进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"⚡ 强制杀死生命周期管理器进程")
                process.kill()
                process.wait()
        
        # 读取输出
        stdout, stderr = process.communicate()
        
        print(f"\n📊 测试结果:")
        print(f"   返回码: {process.returncode}")
        print(f"   标准输出长度: {len(stdout)} 字符")
        print(f"   标准错误长度: {len(stderr)} 字符")
        
        if stderr:
            print(f"\n📝 标准错误输出 (最后20行):")
            stderr_lines = stderr.strip().split('\n')
            for line in stderr_lines[-20:]:
                print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_communication():
    """测试通信机制"""
    print("\n🧪 测试通信机制")
    print("=" * 60)
    
    try:
        from widget_recognition_monitor import WidgetRecognitionMonitor
        import socket
        import json
        
        # 创建监控器
        monitor = WidgetRecognitionMonitor(timeout_threshold=3.0, debug=True)
        
        # 启动监控器（不启动目标进程）
        monitor.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        monitor.socket.bind(('127.0.0.1', 12345))
        monitor.socket.settimeout(1.0)
        monitor.running = True
        
        # 启动监听线程
        monitor.listen_thread = threading.Thread(target=monitor._listen_loop, daemon=True)
        monitor.listen_thread.start()
        
        print(f"✅ 监控器已启动，监听端口: 12345")
        
        # 创建测试客户端
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
        # 测试发送识别开始信号
        start_message = {
            'session_id': 'test_session_001',
            'signal_type': 'recognition_start',
            'timestamp': time.time(),
            'data': {'x': 100, 'y': 200, 'recognition_type': 'test'}
        }
        
        print(f"📤 发送识别开始信号...")
        client_socket.sendto(
            json.dumps(start_message).encode('utf-8'),
            ('127.0.0.1', 12345)
        )
        
        # 等待2秒
        time.sleep(2)
        
        # 测试发送识别结束信号
        end_message = {
            'session_id': 'test_session_001',
            'signal_type': 'recognition_end',
            'timestamp': time.time(),
            'data': {'x': 100, 'y': 200, 'duration': 1.5, 'success': True, 'recognition_type': 'test'}
        }
        
        print(f"📤 发送识别结束信号...")
        client_socket.sendto(
            json.dumps(end_message).encode('utf-8'),
            ('127.0.0.1', 12345)
        )
        
        # 等待1秒
        time.sleep(1)
        
        # 获取统计信息
        stats = monitor.get_statistics()
        print(f"\n📊 通信测试结果:")
        print(f"   总识别次数: {stats['total_recognitions']}")
        print(f"   超时次数: {stats['timeout_count']}")
        print(f"   活跃识别任务: {stats['active_recognitions']}")
        
        # 清理
        client_socket.close()
        monitor.stop()
        
        print(f"✅ 通信测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 通信测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 控件识别监控器测试套件")
    print("=" * 80)
    
    # 信号处理
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在退出...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 测试1: 通信机制
        success1 = test_communication()
        
        # 测试2: 控件识别监控器
        success2 = test_widget_monitor()
        
        # 测试3: 生命周期管理器 + 控件识别监控器
        success3 = test_lifecycle_with_widget_monitor()
        
        print(f"\n✅ 测试完成")
        print(f"📊 测试结果:")
        print(f"   通信机制: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"   控件识别监控器: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"   生命周期管理器集成: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if all([success1, success2, success3]):
            print(f"\n🎉 所有测试通过！")
        else:
            print(f"\n⚠️ 部分测试失败，请检查日志")
            
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试套件异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
