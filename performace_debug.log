[INFO] X11环境检测成功，已加载X11相关库
[INFO] UNI模块从路径加载成功: scripts
[INFO] UNI模块从路径加载成功: scripts
[INFO] 高亮显示模块加载成功
[INFO] Locator生成器加载成功
[UNI] 初始化完成，显示服务器: x11
[INFO] 开始录制会话: session_1753721420955
[INFO] 悬停超时触发: 坐标=(27, 805)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[UNI] 开始查找坐标(27, 805)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2879
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (27, 805)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 标识符匹配(panel): 得分=95
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (27, 805) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (27, 805) 的控件
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 105
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (27, 805) 处的控件...
[DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 805)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 805)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 805)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 805)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1195, 面积: 2116, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1185, 面积: 2116, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1040x46
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1133, 783) 大小288x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(27, 805)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1175, 面积: 2116, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1165, 面积: 2116, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[1]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[2]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[3]: UKUI Panel (frame)
[DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[DEBUG]     向上遍历[4]: ukui-panel (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (27, 805) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (27, 805) 的控件
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=105
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (27, 805) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[UNI] ParentPath第一个节点索引: 2
[UNI] 找到应用程序对象: ukui-panel
[UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2879, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (27, 805), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[UNI] ParentPath第一个节点索引: 2
[UNI] 找到应用程序对象: ukui-panel
[UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[UNI] 控件实际所属窗口: UKUI Panel
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2879, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (27, 805), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': 'UKUI Panel'}
[UNI] 控件名称生成为: 按钮_(0,783)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.239秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 按钮_(0,783) (进程: ukui-panel)
[INFO] 异步高亮显示完成
[TRACE] 🖱️ 原始鼠标事件: 位置=(25, 807) 按钮=left 状态=按下
[INFO] 记录拖动起始点: (25, 807) 按钮=left
[INFO] 鼠标按下: 位置=(25, 807) 按钮=left - 等待拖动检测
[TRACE] 🖱️ 原始鼠标事件: 位置=(25, 807) 按钮=left 状态=释放
[INFO] 鼠标释放检查: 起始(25, 807) 结束(25, 807) 距离=0.0px 阈值=10px
[INFO] 距离不足，不是拖动: 0.0px < 10px
[INFO] 鼠标释放: 位置=(25, 807) 按钮=left - 处理为点击事件
[INFO] 处理左键点击事件: 位置=(25, 807)
[INFO] 🎯 使用悬停缓存控件: 按钮_(0,783) (进程: ukui-panel)
[INFO] 📌 悬停-点击绑定成功
[INFO] ✅ click事件已加入队列: left键 位置=(25, 807) 有控件 (队列大小: 1)
[PROCESS] 📥 处理click事件 (剩余队列: 0)
[INFO] 记录鼠标点击动作: left键 位置=(25, 807)
[ERROR] Locator目录未设置
[JSON] ❌ JSON输出未启用，跳过事件输出
[INFO] 悬停超时触发: 坐标=(130, 343)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[UNI] 开始查找坐标(130, 343)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | ], 进程ID: 4062
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (130, 343)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-menu.ukui-menu
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-menu.ukui-menu'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-panel' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 反向标识符匹配(ukui-menu): 得分=103
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 103
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 103)
[DEBUG] 🔍 高匹配度应用程序 (得分: 103)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-menu' 中查找坐标 (130, 343) 处的控件...
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(8, 181) 大小382x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 2)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x54
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(9, 235) 大小320x540
[DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[1] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 3, 子控件数: 8)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 235) 大小320x536
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 4, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (list): 坐标(15, 235) 大小314x530
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (list)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list, 深度: 5, 子控件数: 53)
[DEBUG]     检查子控件[0] 'unnamed' (list item): 坐标(15, 235) 大小298x44
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[1] 'unnamed' (list item): 坐标(15, 279) 大小298x44
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[2] 'unnamed' (list item): 坐标(15, 323) 大小298x44
[DEBUG]     ✅ 子控件[2] 'unnamed' 包含目标坐标(130, 343)
[DEBUG]     🔍 开始递归搜索子控件[2] 'unnamed' (list item)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list item, 深度: 6, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: list item)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[3] 'unnamed' (list item): 坐标(15, 367) 大小298x44
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[4] 'unnamed' (list item): 坐标(15, 411) 大小298x44
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[5] 'unnamed' (list item): 坐标(15, 455) 大小298x44
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[6] 'unnamed' (list item): 坐标(15, 499) 大小298x44
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[7] 'unnamed' (list item): 坐标(15, 543) 大小298x44
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[8] 'unnamed' (list item): 坐标(15, 587) 大小298x44
[DEBUG]     ❌ 子控件[8] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[9] 'unnamed' (list item): 坐标(15, 631) 大小298x44
[DEBUG]     ❌ 子控件[9] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[10] 'unnamed' (list item): 坐标(15, 675) 大小298x44
[DEBUG]     ❌ 子控件[10] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[11] 'unnamed' (list item): 坐标(15, 719) 大小298x44
[DEBUG]     ❌ 子控件[11] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[12] 'unnamed' (list item): 坐标(15, 763) 大小298x44
[DEBUG]     ❌ 子控件[12] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[13] 'unnamed' (list item): 坐标(15, 807) 大小298x44
[DEBUG]     ❌ 子控件[13] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[14] 'unnamed' (list item): 坐标(15, 851) 大小298x44
[DEBUG]     ❌ 子控件[14] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[15] 'unnamed' (list item): 坐标(15, 895) 大小298x44
[DEBUG]     ❌ 子控件[15] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[16] 'unnamed' (list item): 坐标(15, 939) 大小298x44
[DEBUG]     ❌ 子控件[16] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[17] 'unnamed' (list item): 坐标(15, 983) 大小298x44
[DEBUG]     ❌ 子控件[17] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[18] 'unnamed' (list item): 坐标(15, 1027) 大小298x44
[DEBUG]     ❌ 子控件[18] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[19] 'unnamed' (list item): 坐标(15, 1071) 大小298x44
[DEBUG]     ❌ 子控件[19] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[20] 'unnamed' (list item): 坐标(15, 1115) 大小298x44
[DEBUG]     ❌ 子控件[20] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[21] 'unnamed' (list item): 坐标(15, 1159) 大小298x44
[DEBUG]     ❌ 子控件[21] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[22] 'unnamed' (list item): 坐标(15, 1203) 大小298x44
[DEBUG]     ❌ 子控件[22] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[23] 'unnamed' (list item): 坐标(15, 1247) 大小298x44
[DEBUG]     ❌ 子控件[23] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[24] 'unnamed' (list item): 坐标(15, 1291) 大小298x44
[DEBUG]     ❌ 子控件[24] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[25] 'unnamed' (list item): 坐标(15, 1335) 大小298x44
[DEBUG]     ❌ 子控件[25] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[26] 'unnamed' (list item): 坐标(15, 1379) 大小298x44
[DEBUG]     ❌ 子控件[26] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[27] 'unnamed' (list item): 坐标(15, 1423) 大小298x44
[DEBUG]     ❌ 子控件[27] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[28] 'unnamed' (list item): 坐标(15, 1467) 大小298x44
[DEBUG]     ❌ 子控件[28] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[29] 'unnamed' (list item): 坐标(15, 1511) 大小298x44
[DEBUG]     ❌ 子控件[29] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[30] 'unnamed' (list item): 坐标(15, 1555) 大小298x44
[DEBUG]     ❌ 子控件[30] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[31] 'unnamed' (list item): 坐标(15, 1599) 大小298x44
[DEBUG]     ❌ 子控件[31] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[32] 'unnamed' (list item): 坐标(15, 1643) 大小298x44
[DEBUG]     ❌ 子控件[32] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[33] 'unnamed' (list item): 坐标(15, 1687) 大小298x44
[DEBUG]     ❌ 子控件[33] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[34] 'unnamed' (list item): 坐标(15, 1731) 大小298x44
[DEBUG]     ❌ 子控件[34] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[35] 'unnamed' (list item): 坐标(15, 1775) 大小298x44
[DEBUG]     ❌ 子控件[35] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[36] 'unnamed' (list item): 坐标(15, 1819) 大小298x44
[DEBUG]     ❌ 子控件[36] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[37] 'unnamed' (list item): 坐标(15, 1863) 大小298x44
[DEBUG]     ❌ 子控件[37] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[38] 'unnamed' (list item): 坐标(15, 1907) 大小298x44
[DEBUG]     ❌ 子控件[38] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[39] 'unnamed' (list item): 坐标(15, 1951) 大小298x44
[DEBUG]     ❌ 子控件[39] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[40] 'unnamed' (list item): 坐标(15, 1995) 大小298x44
[DEBUG]     ❌ 子控件[40] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[41] 'unnamed' (list item): 坐标(15, 2039) 大小298x44
[DEBUG]     ❌ 子控件[41] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[42] 'unnamed' (list item): 坐标(15, 2083) 大小298x44
[DEBUG]     ❌ 子控件[42] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[43] 'unnamed' (list item): 坐标(15, 2127) 大小298x44
[DEBUG]     ❌ 子控件[43] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[44] 'unnamed' (list item): 坐标(15, 2171) 大小298x44
[DEBUG]     ❌ 子控件[44] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[45] 'unnamed' (list item): 坐标(15, 2215) 大小298x44
[DEBUG]     ❌ 子控件[45] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[46] 'unnamed' (list item): 坐标(15, 2259) 大小298x44
[DEBUG]     ❌ 子控件[46] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[47] 'unnamed' (list item): 坐标(15, 2303) 大小298x44
[DEBUG]     ❌ 子控件[47] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[48] 'unnamed' (list item): 坐标(15, 2347) 大小298x44
[DEBUG]     ❌ 子控件[48] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[49] 'unnamed' (list item): 坐标(15, 2391) 大小298x44
[DEBUG]     ❌ 子控件[49] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[50] 'unnamed' (list item): 坐标(15, 2435) 大小298x44
[DEBUG]     ❌ 子控件[50] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[51] 'unnamed' (list item): 坐标(15, 2479) 大小298x44
[DEBUG]     ❌ 子控件[51] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[52] 'unnamed' (list item): 坐标(15, 2523) 大小298x44
[DEBUG]     ❌ 子控件[52] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1185, 面积: 13112, 分支: 2)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1175, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[3] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[4] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[5] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[6] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[7] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1165, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1155, 面积: 13112, 分支: 1)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(330, 181) 大小1x593
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(332, 181) 大小55x594
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(130, 343)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1145, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1135, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: list item)
[DEBUG]     向上遍历[0]: unnamed (list)
[DEBUG]     向上遍历[1]: unnamed (filler)
[DEBUG]     向上遍历[2]: unnamed (filler)
[DEBUG]     向上遍历[3]: unnamed (filler)
[DEBUG]     向上遍历[4]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[5]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[6]: ukui-menu (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (list item) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: list item)
[DEBUG] ✅ 在应用程序 'ukui-menu' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 103)
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=103
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (130, 343) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [list item | ]
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 2]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 2, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 323, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 2], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001002', 'RecordPosition': (130, 343), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 2]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 控件实际所属窗口: 
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 2, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 323, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 2], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001002', 'RecordPosition': (130, 343), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': ''}
[UNI] 控件名称生成为: 列表项_(15,323)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.261秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 列表项_(15,323) (进程: ukui-menu)
[INFO] 异步高亮显示完成
[INFO] 悬停超时触发: 坐标=(144, 385)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[UNI] 开始查找坐标(144, 385)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | ], 进程ID: 4062
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (144, 385)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-menu.ukui-menu
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-menu.ukui-menu'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-panel' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 反向标识符匹配(ukui-menu): 得分=103
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 103
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 103)
[DEBUG] 🔍 高匹配度应用程序 (得分: 103)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-menu' 中查找坐标 (144, 385) 处的控件...
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(8, 181) 大小382x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 2)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x54
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(9, 235) 大小320x540
[DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[1] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 3, 子控件数: 8)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 235) 大小320x536
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 4, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (list): 坐标(15, 235) 大小314x530
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (list)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list, 深度: 5, 子控件数: 53)
[DEBUG]     检查子控件[0] 'unnamed' (list item): 坐标(15, 235) 大小298x44
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[1] 'unnamed' (list item): 坐标(15, 279) 大小298x44
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[2] 'unnamed' (list item): 坐标(15, 323) 大小298x44
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[3] 'unnamed' (list item): 坐标(15, 367) 大小298x44
[DEBUG]     ✅ 子控件[3] 'unnamed' 包含目标坐标(144, 385)
[DEBUG]     🔍 开始递归搜索子控件[3] 'unnamed' (list item)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list item, 深度: 6, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: list item)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[4] 'unnamed' (list item): 坐标(15, 411) 大小298x44
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[5] 'unnamed' (list item): 坐标(15, 455) 大小298x44
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[6] 'unnamed' (list item): 坐标(15, 499) 大小298x44
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[7] 'unnamed' (list item): 坐标(15, 543) 大小298x44
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[8] 'unnamed' (list item): 坐标(15, 587) 大小298x44
[DEBUG]     ❌ 子控件[8] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[9] 'unnamed' (list item): 坐标(15, 631) 大小298x44
[DEBUG]     ❌ 子控件[9] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[10] 'unnamed' (list item): 坐标(15, 675) 大小298x44
[DEBUG]     ❌ 子控件[10] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[11] 'unnamed' (list item): 坐标(15, 719) 大小298x44
[DEBUG]     ❌ 子控件[11] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[12] 'unnamed' (list item): 坐标(15, 763) 大小298x44
[DEBUG]     ❌ 子控件[12] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[13] 'unnamed' (list item): 坐标(15, 807) 大小298x44
[DEBUG]     ❌ 子控件[13] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[14] 'unnamed' (list item): 坐标(15, 851) 大小298x44
[DEBUG]     ❌ 子控件[14] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[15] 'unnamed' (list item): 坐标(15, 895) 大小298x44
[DEBUG]     ❌ 子控件[15] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[16] 'unnamed' (list item): 坐标(15, 939) 大小298x44
[DEBUG]     ❌ 子控件[16] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[17] 'unnamed' (list item): 坐标(15, 983) 大小298x44
[DEBUG]     ❌ 子控件[17] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[18] 'unnamed' (list item): 坐标(15, 1027) 大小298x44
[DEBUG]     ❌ 子控件[18] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[19] 'unnamed' (list item): 坐标(15, 1071) 大小298x44
[DEBUG]     ❌ 子控件[19] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[20] 'unnamed' (list item): 坐标(15, 1115) 大小298x44
[DEBUG]     ❌ 子控件[20] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[21] 'unnamed' (list item): 坐标(15, 1159) 大小298x44
[DEBUG]     ❌ 子控件[21] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[22] 'unnamed' (list item): 坐标(15, 1203) 大小298x44
[DEBUG]     ❌ 子控件[22] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[23] 'unnamed' (list item): 坐标(15, 1247) 大小298x44
[DEBUG]     ❌ 子控件[23] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[24] 'unnamed' (list item): 坐标(15, 1291) 大小298x44
[DEBUG]     ❌ 子控件[24] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[25] 'unnamed' (list item): 坐标(15, 1335) 大小298x44
[DEBUG]     ❌ 子控件[25] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[26] 'unnamed' (list item): 坐标(15, 1379) 大小298x44
[DEBUG]     ❌ 子控件[26] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[27] 'unnamed' (list item): 坐标(15, 1423) 大小298x44
[DEBUG]     ❌ 子控件[27] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[28] 'unnamed' (list item): 坐标(15, 1467) 大小298x44
[DEBUG]     ❌ 子控件[28] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[29] 'unnamed' (list item): 坐标(15, 1511) 大小298x44
[DEBUG]     ❌ 子控件[29] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[30] 'unnamed' (list item): 坐标(15, 1555) 大小298x44
[DEBUG]     ❌ 子控件[30] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[31] 'unnamed' (list item): 坐标(15, 1599) 大小298x44
[DEBUG]     ❌ 子控件[31] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[32] 'unnamed' (list item): 坐标(15, 1643) 大小298x44
[DEBUG]     ❌ 子控件[32] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[33] 'unnamed' (list item): 坐标(15, 1687) 大小298x44
[DEBUG]     ❌ 子控件[33] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[34] 'unnamed' (list item): 坐标(15, 1731) 大小298x44
[DEBUG]     ❌ 子控件[34] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[35] 'unnamed' (list item): 坐标(15, 1775) 大小298x44
[DEBUG]     ❌ 子控件[35] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[36] 'unnamed' (list item): 坐标(15, 1819) 大小298x44
[DEBUG]     ❌ 子控件[36] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[37] 'unnamed' (list item): 坐标(15, 1863) 大小298x44
[DEBUG]     ❌ 子控件[37] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[38] 'unnamed' (list item): 坐标(15, 1907) 大小298x44
[DEBUG]     ❌ 子控件[38] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[39] 'unnamed' (list item): 坐标(15, 1951) 大小298x44
[DEBUG]     ❌ 子控件[39] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[40] 'unnamed' (list item): 坐标(15, 1995) 大小298x44
[DEBUG]     ❌ 子控件[40] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[41] 'unnamed' (list item): 坐标(15, 2039) 大小298x44
[DEBUG]     ❌ 子控件[41] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[42] 'unnamed' (list item): 坐标(15, 2083) 大小298x44
[DEBUG]     ❌ 子控件[42] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[43] 'unnamed' (list item): 坐标(15, 2127) 大小298x44
[DEBUG]     ❌ 子控件[43] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[44] 'unnamed' (list item): 坐标(15, 2171) 大小298x44
[DEBUG]     ❌ 子控件[44] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[45] 'unnamed' (list item): 坐标(15, 2215) 大小298x44
[DEBUG]     ❌ 子控件[45] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[46] 'unnamed' (list item): 坐标(15, 2259) 大小298x44
[DEBUG]     ❌ 子控件[46] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[47] 'unnamed' (list item): 坐标(15, 2303) 大小298x44
[DEBUG]     ❌ 子控件[47] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[48] 'unnamed' (list item): 坐标(15, 2347) 大小298x44
[DEBUG]     ❌ 子控件[48] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[49] 'unnamed' (list item): 坐标(15, 2391) 大小298x44
[DEBUG]     ❌ 子控件[49] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[50] 'unnamed' (list item): 坐标(15, 2435) 大小298x44
[DEBUG]     ❌ 子控件[50] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[51] 'unnamed' (list item): 坐标(15, 2479) 大小298x44
[DEBUG]     ❌ 子控件[51] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[52] 'unnamed' (list item): 坐标(15, 2523) 大小298x44
[DEBUG]     ❌ 子控件[52] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1185, 面积: 13112, 分支: 3)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1175, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[3] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[4] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[5] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[6] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[7] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1165, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1155, 面积: 13112, 分支: 1)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(330, 181) 大小1x593
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(332, 181) 大小55x594
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(144, 385)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1145, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1135, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: list item)
[DEBUG]     向上遍历[0]: unnamed (list)
[DEBUG]     向上遍历[1]: unnamed (filler)
[DEBUG]     向上遍历[2]: unnamed (filler)
[DEBUG]     向上遍历[3]: unnamed (filler)
[DEBUG]     向上遍历[4]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[5]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[6]: ukui-menu (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (list item) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: list item)
[DEBUG] ✅ 在应用程序 'ukui-menu' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 103)
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=103
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (144, 385) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [list item | ]
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 3]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 3, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 367, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 3], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001003', 'RecordPosition': (144, 385), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 3]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 控件实际所属窗口: 
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 3, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 367, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 3], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001003', 'RecordPosition': (144, 385), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': ''}
[UNI] 控件名称生成为: 列表项_(15,367)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.294秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 列表项_(15,367) (进程: ukui-menu)
[INFO] 异步高亮显示完成
[INFO] 悬停超时触发: 坐标=(146, 443)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[UNI] 开始查找坐标(146, 443)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | ], 进程ID: 4062
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (146, 443)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-menu.ukui-menu
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-menu.ukui-menu'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-panel' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 反向标识符匹配(ukui-menu): 得分=103
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 103
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 103)
[DEBUG] 🔍 高匹配度应用程序 (得分: 103)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-menu' 中查找坐标 (146, 443) 处的控件...
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(8, 181) 大小382x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x594
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 2)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x54
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(9, 235) 大小320x540
[DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[1] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 3, 子控件数: 8)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 235) 大小320x536
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 4, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (list): 坐标(15, 235) 大小314x530
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (list)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list, 深度: 5, 子控件数: 53)
[DEBUG]     检查子控件[0] 'unnamed' (list item): 坐标(15, 235) 大小298x44
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[1] 'unnamed' (list item): 坐标(15, 279) 大小298x44
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[2] 'unnamed' (list item): 坐标(15, 323) 大小298x44
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[3] 'unnamed' (list item): 坐标(15, 367) 大小298x44
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[4] 'unnamed' (list item): 坐标(15, 411) 大小298x44
[DEBUG]     ✅ 子控件[4] 'unnamed' 包含目标坐标(146, 443)
[DEBUG]     🔍 开始递归搜索子控件[4] 'unnamed' (list item)
[DEBUG]     找到包含坐标的元素: unnamed (角色: list item, 深度: 6, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: list item)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[5] 'unnamed' (list item): 坐标(15, 455) 大小298x44
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[6] 'unnamed' (list item): 坐标(15, 499) 大小298x44
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[7] 'unnamed' (list item): 坐标(15, 543) 大小298x44
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[8] 'unnamed' (list item): 坐标(15, 587) 大小298x44
[DEBUG]     ❌ 子控件[8] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[9] 'unnamed' (list item): 坐标(15, 631) 大小298x44
[DEBUG]     ❌ 子控件[9] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[10] 'unnamed' (list item): 坐标(15, 675) 大小298x44
[DEBUG]     ❌ 子控件[10] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[11] 'unnamed' (list item): 坐标(15, 719) 大小298x44
[DEBUG]     ❌ 子控件[11] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[12] 'unnamed' (list item): 坐标(15, 763) 大小298x44
[DEBUG]     ❌ 子控件[12] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[13] 'unnamed' (list item): 坐标(15, 807) 大小298x44
[DEBUG]     ❌ 子控件[13] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[14] 'unnamed' (list item): 坐标(15, 851) 大小298x44
[DEBUG]     ❌ 子控件[14] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[15] 'unnamed' (list item): 坐标(15, 895) 大小298x44
[DEBUG]     ❌ 子控件[15] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[16] 'unnamed' (list item): 坐标(15, 939) 大小298x44
[DEBUG]     ❌ 子控件[16] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[17] 'unnamed' (list item): 坐标(15, 983) 大小298x44
[DEBUG]     ❌ 子控件[17] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[18] 'unnamed' (list item): 坐标(15, 1027) 大小298x44
[DEBUG]     ❌ 子控件[18] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[19] 'unnamed' (list item): 坐标(15, 1071) 大小298x44
[DEBUG]     ❌ 子控件[19] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[20] 'unnamed' (list item): 坐标(15, 1115) 大小298x44
[DEBUG]     ❌ 子控件[20] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[21] 'unnamed' (list item): 坐标(15, 1159) 大小298x44
[DEBUG]     ❌ 子控件[21] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[22] 'unnamed' (list item): 坐标(15, 1203) 大小298x44
[DEBUG]     ❌ 子控件[22] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[23] 'unnamed' (list item): 坐标(15, 1247) 大小298x44
[DEBUG]     ❌ 子控件[23] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[24] 'unnamed' (list item): 坐标(15, 1291) 大小298x44
[DEBUG]     ❌ 子控件[24] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[25] 'unnamed' (list item): 坐标(15, 1335) 大小298x44
[DEBUG]     ❌ 子控件[25] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[26] 'unnamed' (list item): 坐标(15, 1379) 大小298x44
[DEBUG]     ❌ 子控件[26] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[27] 'unnamed' (list item): 坐标(15, 1423) 大小298x44
[DEBUG]     ❌ 子控件[27] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[28] 'unnamed' (list item): 坐标(15, 1467) 大小298x44
[DEBUG]     ❌ 子控件[28] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[29] 'unnamed' (list item): 坐标(15, 1511) 大小298x44
[DEBUG]     ❌ 子控件[29] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[30] 'unnamed' (list item): 坐标(15, 1555) 大小298x44
[DEBUG]     ❌ 子控件[30] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[31] 'unnamed' (list item): 坐标(15, 1599) 大小298x44
[DEBUG]     ❌ 子控件[31] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[32] 'unnamed' (list item): 坐标(15, 1643) 大小298x44
[DEBUG]     ❌ 子控件[32] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[33] 'unnamed' (list item): 坐标(15, 1687) 大小298x44
[DEBUG]     ❌ 子控件[33] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[34] 'unnamed' (list item): 坐标(15, 1731) 大小298x44
[DEBUG]     ❌ 子控件[34] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[35] 'unnamed' (list item): 坐标(15, 1775) 大小298x44
[DEBUG]     ❌ 子控件[35] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[36] 'unnamed' (list item): 坐标(15, 1819) 大小298x44
[DEBUG]     ❌ 子控件[36] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[37] 'unnamed' (list item): 坐标(15, 1863) 大小298x44
[DEBUG]     ❌ 子控件[37] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[38] 'unnamed' (list item): 坐标(15, 1907) 大小298x44
[DEBUG]     ❌ 子控件[38] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[39] 'unnamed' (list item): 坐标(15, 1951) 大小298x44
[DEBUG]     ❌ 子控件[39] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[40] 'unnamed' (list item): 坐标(15, 1995) 大小298x44
[DEBUG]     ❌ 子控件[40] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[41] 'unnamed' (list item): 坐标(15, 2039) 大小298x44
[DEBUG]     ❌ 子控件[41] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[42] 'unnamed' (list item): 坐标(15, 2083) 大小298x44
[DEBUG]     ❌ 子控件[42] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[43] 'unnamed' (list item): 坐标(15, 2127) 大小298x44
[DEBUG]     ❌ 子控件[43] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[44] 'unnamed' (list item): 坐标(15, 2171) 大小298x44
[DEBUG]     ❌ 子控件[44] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[45] 'unnamed' (list item): 坐标(15, 2215) 大小298x44
[DEBUG]     ❌ 子控件[45] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[46] 'unnamed' (list item): 坐标(15, 2259) 大小298x44
[DEBUG]     ❌ 子控件[46] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[47] 'unnamed' (list item): 坐标(15, 2303) 大小298x44
[DEBUG]     ❌ 子控件[47] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[48] 'unnamed' (list item): 坐标(15, 2347) 大小298x44
[DEBUG]     ❌ 子控件[48] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[49] 'unnamed' (list item): 坐标(15, 2391) 大小298x44
[DEBUG]     ❌ 子控件[49] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[50] 'unnamed' (list item): 坐标(15, 2435) 大小298x44
[DEBUG]     ❌ 子控件[50] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[51] 'unnamed' (list item): 坐标(15, 2479) 大小298x44
[DEBUG]     ❌ 子控件[51] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[52] 'unnamed' (list item): 坐标(15, 2523) 大小298x44
[DEBUG]     ❌ 子控件[52] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1185, 面积: 13112, 分支: 4)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1175, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[3] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[4] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[5] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[6] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[7] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1165, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1155, 面积: 13112, 分支: 1)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(330, 181) 大小1x593
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(332, 181) 大小55x594
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(146, 443)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1145, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: list item, 得分: 1135, 面积: 13112, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: list item)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: list item)
[DEBUG]     向上遍历[0]: unnamed (list)
[DEBUG]     向上遍历[1]: unnamed (filler)
[DEBUG]     向上遍历[2]: unnamed (filler)
[DEBUG]     向上遍历[3]: unnamed (filler)
[DEBUG]     向上遍历[4]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[5]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[6]: ukui-menu (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (list item) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: list item)
[DEBUG] ✅ 在应用程序 'ukui-menu' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 103)
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='ukui-menu.ukui-menu'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=103
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (146, 443) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [list item | ]
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 4]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 4, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 411, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 4], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001004', 'RecordPosition': (146, 443), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 4]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: ukui-menu
[UNI] ParentPath第一个节点对象:  (角色: frame)
[UNI] 控件实际所属窗口: 
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3646, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 4, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 411, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 4], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001004', 'RecordPosition': (146, 443), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': ''}
[UNI] 控件名称生成为: 列表项_(15,411)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.243秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 列表项_(15,411) (进程: ukui-menu)
[INFO] 异步高亮显示完成
[TRACE] 🖱️ 原始鼠标事件: 位置=(146, 446) 按钮=left 状态=按下
[INFO] 记录拖动起始点: (146, 446) 按钮=left
[INFO] 鼠标按下: 位置=(146, 446) 按钮=left - 等待拖动检测
[TRACE] 🖱️ 原始鼠标事件: 位置=(146, 446) 按钮=left 状态=释放
[INFO] 鼠标释放检查: 起始(146, 446) 结束(146, 446) 距离=0.0px 阈值=10px
[INFO] 距离不足，不是拖动: 0.0px < 10px
[INFO] 鼠标释放: 位置=(146, 446) 按钮=left - 处理为点击事件
[INFO] 处理左键点击事件: 位置=(146, 446)
[INFO] 🎯 使用悬停缓存控件: 列表项_(15,411) (进程: ukui-menu)
[INFO] 📌 悬停-点击绑定成功
[INFO] ✅ click事件已加入队列: left键 位置=(146, 446) 有控件 (队列大小: 1)
[PROCESS] 📥 处理click事件 (剩余队列: 0)
[INFO] 记录鼠标点击动作: left键 位置=(146, 446)
[ERROR] Locator目录未设置
[JSON] ❌ JSON输出未启用，跳过事件输出
[INFO] 悬停超时触发: 坐标=(465, 487)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 51
[UNI] 开始查找坐标(465, 487)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 51
[UNI] 获取到活动窗口: [frame | ], 进程ID: 4062
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (465, 487)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: kylin-calculator.kylin-calculator
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 51
[DEBUG] 目标窗口类名: 'kylin-calculator.kylin-calculator'
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-process-manager (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-process-manager' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-vpn (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-vpn' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-device-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-device-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-nm (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-nm' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-nm (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-nm' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: Kylin Note (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'Kylin Note' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='计算器' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 传统特殊规则匹配: 得分=95
[DEBUG] 应用程序[50]: '计算器' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: 计算器 (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 '计算器' 中查找坐标 (465, 487) 处的控件...
[DEBUG]     找到包含坐标的元素: 计算器 (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(432, 154) 大小432x628
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(465, 487)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 1, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(432, 154) 大小432x38
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(432, 192) 大小432x268
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(432, 460) 大小432x322
[DEBUG]     ✅ 子控件[2] 'unnamed' 包含目标坐标(465, 487)
[DEBUG]     🔍 开始递归搜索子控件[2] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(432, 460) 大小432x322
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(465, 487)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 3, 子控件数: 19)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(436, 716) 大小211x62
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[1] 'unnamed' (push button): 坐标(436, 652) 大小105x62
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(543, 652) 大小104x62
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[3] 'unnamed' (push button): 坐标(649, 652) 大小105x62
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[4] 'unnamed' (push button): 坐标(436, 588) 大小105x62
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[5] 'unnamed' (push button): 坐标(543, 588) 大小104x62
[DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[6] 'unnamed' (push button): 坐标(649, 588) 大小105x62
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[7] 'unnamed' (push button): 坐标(436, 524) 大小105x62
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[8] 'unnamed' (push button): 坐标(543, 524) 大小104x62
[DEBUG]     ❌ 子控件[8] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[9] 'unnamed' (push button): 坐标(649, 524) 大小105x62
[DEBUG]     ❌ 子控件[9] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[10] 'unnamed' (push button): 坐标(436, 460) 大小105x62
[DEBUG]     ✅ 子控件[10] 'unnamed' 包含目标坐标(465, 487)
[DEBUG]     🔍 开始递归搜索子控件[10] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     检查子控件[11] 'unnamed' (push button): 坐标(543, 460) 大小104x62
[DEBUG]     ❌ 子控件[11] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[12] 'unnamed' (push button): 坐标(649, 460) 大小105x62
[DEBUG]     ❌ 子控件[12] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[13] 'unnamed' (push button): 坐标(756, 460) 大小104x62
[DEBUG]     ❌ 子控件[13] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[14] 'unnamed' (push button): 坐标(756, 524) 大小104x62
[DEBUG]     ❌ 子控件[14] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[15] 'unnamed' (push button): 坐标(756, 588) 大小104x62
[DEBUG]     ❌ 子控件[15] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[16] 'unnamed' (push button): 坐标(756, 652) 大小104x62
[DEBUG]     ❌ 子控件[16] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[17] 'unnamed' (push button): 坐标(756, 716) 大小104x62
[DEBUG]     ❌ 子控件[17] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     检查子控件[18] 'unnamed' (push button): 坐标(649, 716) 大小105x62
[DEBUG]     ❌ 子控件[18] 'unnamed' 不包含目标坐标(465, 487)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1195, 面积: 6510, 分支: 10)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1185, 面积: 6510, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1175, 面积: 6510, 分支: 2)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1165, 面积: 6510, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (filler)
[DEBUG]     向上遍历[1]: unnamed (filler)
[DEBUG]     向上遍历[2]: unnamed (filler)
[DEBUG]     向上遍历[3]: 计算器 (frame)
[DEBUG]     📋 找到主窗口: 计算器 (frame)
[DEBUG]     向上遍历[4]: 计算器 (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 '计算器' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 95)
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=95
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (465, 487) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 控件ParentPath: [0, 0, 2, 0, 10]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: 计算器
[UNI] ParentPath第一个节点对象: 计算器 (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 395573, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 10, 'ChildrenCount': 0, 'ProcessName': 'kylin-calculato', 'Coords': {'x': 436, 'y': 460, 'width': 105, 'height': 62}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 2, 0, 10], 'ParentCount': 5, 'Key': 'NNA-DNA-P002010', 'RecordPosition': (465, 487), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 2, 0, 10]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: 计算器
[UNI] ParentPath第一个节点对象: 计算器 (角色: frame)
[UNI] 控件实际所属窗口: 计算器
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 395573, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 10, 'ChildrenCount': 0, 'ProcessName': 'kylin-calculato', 'Coords': {'x': 436, 'y': 460, 'width': 105, 'height': 62}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 2, 0, 10], 'ParentCount': 5, 'Key': 'NNA-DNA-P002010', 'RecordPosition': (465, 487), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': '计算器'}
[UNI] 控件名称生成为: 按钮_(436,460)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.232秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 按钮_(436,460) (进程: kylin-calculato)
[INFO] 异步高亮显示完成
[TRACE] 🖱️ 原始鼠标事件: 位置=(465, 488) 按钮=left 状态=按下
[INFO] 记录拖动起始点: (465, 488) 按钮=left
[INFO] 鼠标按下: 位置=(465, 488) 按钮=left - 等待拖动检测
[TRACE] 🖱️ 原始鼠标事件: 位置=(464, 488) 按钮=left 状态=释放
[INFO] 鼠标释放检查: 起始(465, 488) 结束(464, 488) 距离=1.0px 阈值=10px
[INFO] 距离不足，不是拖动: 1.0px < 10px
[INFO] 鼠标释放: 位置=(464, 488) 按钮=left - 处理为点击事件
[INFO] 处理左键点击事件: 位置=(464, 488)
[INFO] 🎯 使用悬停缓存控件: 按钮_(436,460) (进程: kylin-calculato)
[INFO] 📌 悬停-点击绑定成功
[INFO] ✅ click事件已加入队列: left键 位置=(464, 488) 有控件 (队列大小: 1)
[PROCESS] 📥 处理click事件 (剩余队列: 0)
[INFO] 记录鼠标点击动作: left键 位置=(464, 488)
[ERROR] Locator目录未设置
[JSON] ❌ JSON输出未启用，跳过事件输出
[INFO] 悬停超时触发: 坐标=(844, 180)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 51
[UNI] 开始查找坐标(844, 180)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 51
[UNI] 获取到活动窗口: [frame | kylin@kylin-pc: ~/kylin-robot-ide], 进程ID: 26933
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (844, 180)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: kylin-calculator.kylin-calculator
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 51
[DEBUG] 目标窗口类名: 'kylin-calculator.kylin-calculator'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 51
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-process-manager (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-process-manager' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-vpn (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-vpn' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: kylin-device-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-device-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-nm (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-nm' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: kylin-nm (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-nm' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: Kylin Note (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'Kylin Note' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='计算器' vs 窗口类名='kylin-calculator.kylin-calculator'
[DEBUG]   过滤通用标识符: 'kylin'
[DEBUG]   提取的应用标识符: ['kylin-calculator', 'calculator.kylin', 'calculator', 'kylin-calculator.kylin-calculator']
[DEBUG]   ✅ 传统特殊规则匹配: 得分=95
[DEBUG] 应用程序[50]: '计算器' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: 计算器 (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 '计算器' 中查找坐标 (844, 180) 处的控件...
[DEBUG]     找到包含坐标的元素: 计算器 (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(432, 154) 大小432x628
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(844, 180)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 1, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(432, 154) 大小432x38
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(844, 180)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 8)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(440, 161) 大小24x24
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[1] '计算器—标准型' (label): 坐标(472, 162) 大小126x22
[DEBUG]     ❌ 子控件[1] '计算器—标准型' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(728, 158) 大小30x30
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[3] 'unnamed' (push button): 坐标(796, 158) 大小30x30
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[4] 'unnamed' (push button): 坐标(0, 0) 大小0x0
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[5] 'unnamed' (push button): 坐标(830, 158) 大小30x30
[DEBUG]     ✅ 子控件[5] 'unnamed' 包含目标坐标(844, 180)
[DEBUG]     🔍 开始递归搜索子控件[5] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 3, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     检查子控件[6] 'unnamed' (filler): 坐标(432, 154) 大小100x30
[DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[7] 'unnamed' (push button): 坐标(762, 158) 大小30x30
[DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1185, 面积: 900, 分支: 5)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(432, 192) 大小432x268
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(432, 460) 大小432x322
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(844, 180)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1175, 面积: 900, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1165, 面积: 900, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (filler)
[DEBUG]     向上遍历[1]: unnamed (filler)
[DEBUG]     向上遍历[2]: 计算器 (frame)
[DEBUG]     📋 找到主窗口: 计算器 (frame)
[DEBUG]     向上遍历[3]: 计算器 (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 '计算器' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 95)
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=95
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (844, 180) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 控件ParentPath: [0, 0, 0, 5]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: 计算器
[UNI] ParentPath第一个节点对象: 计算器 (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 395573, 'Rolename': 'push button', 'Description': '关闭', 'Index_in_parent': 5, 'ChildrenCount': 0, 'ProcessName': 'kylin-calculato', 'Coords': {'x': 830, 'y': 158, 'width': 30, 'height': 30}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 0, 5], 'ParentCount': 4, 'Key': 'NNA-D关闭-P0005', 'RecordPosition': (844, 180), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 0, 5]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: 计算器
[UNI] ParentPath第一个节点对象: 计算器 (角色: frame)
[UNI] 控件实际所属窗口: 计算器
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 395573, 'Rolename': 'push button', 'Description': '关闭', 'Index_in_parent': 5, 'ChildrenCount': 0, 'ProcessName': 'kylin-calculato', 'Coords': {'x': 830, 'y': 158, 'width': 30, 'height': 30}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 0, 5], 'ParentCount': 4, 'Key': 'NNA-D关闭-P0005', 'RecordPosition': (844, 180), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': '计算器'}
[UNI] 控件名称生成为: 按钮_(830,158)
[UNI] 控件信息验证通过
[INFO] 控件识别完成，耗时: 0.215秒
[INFO] 开始异步高亮显示...
[INFO] 🎯 悬停识别成功并缓存: 按钮_(830,158) (进程: kylin-calculato)
[INFO] 异步高亮显示完成
[TRACE] 🖱️ 原始鼠标事件: 位置=(844, 179) 按钮=left 状态=按下
[INFO] 记录拖动起始点: (844, 179) 按钮=left
[INFO] 鼠标按下: 位置=(844, 179) 按钮=left - 等待拖动检测
[TRACE] 🖱️ 原始鼠标事件: 位置=(844, 179) 按钮=left 状态=释放
[INFO] 鼠标释放检查: 起始(844, 179) 结束(844, 179) 距离=0.0px 阈值=10px
[INFO] 距离不足，不是拖动: 0.0px < 10px
[INFO] 鼠标释放: 位置=(844, 179) 按钮=left - 处理为点击事件
[INFO] 处理左键点击事件: 位置=(844, 179)
[INFO] 🎯 使用悬停缓存控件: 按钮_(830,158) (进程: kylin-calculato)
[INFO] 📌 悬停-点击绑定成功
[INFO] ✅ click事件已加入队列: left键 位置=(844, 179) 有控件 (队列大小: 1)
[PROCESS] 📥 处理click事件 (剩余队列: 0)
[INFO] 记录鼠标点击动作: left键 位置=(844, 179)
[ERROR] Locator目录未设置
[JSON] ❌ JSON输出未启用，跳过事件输出
[TRACE] 🖱️ 原始鼠标事件: 位置=(1055, 307) 按钮=left 状态=按下
[INFO] 记录拖动起始点: (1055, 307) 按钮=left
[INFO] 鼠标按下: 位置=(1055, 307) 按钮=left - 等待拖动检测
[TRACE] 🖱️ 原始鼠标事件: 位置=(1055, 307) 按钮=left 状态=释放
[INFO] 鼠标释放检查: 起始(1055, 307) 结束(1055, 307) 距离=0.0px 阈值=10px
[INFO] 距离不足，不是拖动: 0.0px < 10px
[INFO] 鼠标释放: 位置=(1055, 307) 按钮=left - 处理为点击事件
[INFO] 处理左键点击事件: 位置=(1055, 307)
[INFO] ⚡ 无悬停缓存，使用坐标点信息: (1055, 307)
[INFO] ✅ click事件已加入队列: left键 位置=(1055, 307) 无控件 (队列大小: 1)
[PROCESS] 📥 处理click事件 (剩余队列: 0)
[INFO] 记录鼠标点击动作: left键 位置=(1055, 307)
[JSON] ❌ JSON输出未启用，跳过事件输出
[INFO] 悬停超时触发: 坐标=(1101, 299)
[INFO] 开始控件识别...
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[UNI] 开始查找坐标(1101, 299)处的控件
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[DEBUG] 使用缓存的桌面对象
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | kylin@kylin-pc: ~/kylin-robot-ide], 进程ID: 26933
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (1101, 299)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: mate-terminal.Mate-terminal
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'mate-terminal.Mate-terminal'
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[1]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'panel-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[4]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'ukuismserver' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: 'ukui-kwin' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[8]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[9]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=3
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 3
[DEBUG] ✅ 找到匹配应用程序: vdclient (匹配得分: 3)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'vdclient' (得分: 3 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: 'ukui-watermark' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[12]: 'kscreen_backend_launcher' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kscreen_backend_launcher (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kscreen_backend_launcher' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[13]: 'kylin-process-manager' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-process-manager (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-process-manager' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'sogou-qimpanel-watchdog' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[17]: 'ukui-upower' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[18]: 'ukui-notifications' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[21]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'ukui-powermanagement' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'ukui-sidebar' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: 'ukui-power-manager-tray' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[26]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[27]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[29]: 'ukui-volume-control-applet-qt' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[31]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[35]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: 'ukui-search-service-dir-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[37]: 'ukui-search-app-data-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[39]: 'ukui-search' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[42]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='mate-terminal.mate-terminal'
=== 全自动鼠标键盘事件录制器 ===
存储路径: recordings
调试模式: False
录制时长: 300秒

依赖检查:
  pynput可用: True
  UNI模块可用: True
  高亮显示可用: True
  悬停检测启用: True

开始录制...
请在桌面上进行鼠标点击和键盘操作
录制过程中会自动获取点击位置的控件信息
✨ 悬停检测已启用：鼠标悬停0.5秒后会自动识别控件并高亮显示

[INFO] 开始录制，预计持续 300 秒
[INFO] 开始时间: 00:50:21
[INFO] 预计结束时间: 00:55:21
[INFO] 录制进度: 1/300 秒 (0.3%)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
{"type":"keyboard","timestamp":1753721435.9064336,"data":{"event_type":"key_press","timestamp":1753721435.9061203,"key":"c","modifiers":["ctrl"],"key_combination":"ctrl+c"}}
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 反向标识符匹配(mate-terminal): 得分=111
[DEBUG] 应用程序[44]: 'mate-terminal' -> 匹配得分: 111
[DEBUG] ✅ 找到匹配应用程序: mate-terminal (匹配得分: 111)
[DEBUG] 🔍 高匹配度应用程序 (得分: 111)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'mate-terminal' 中查找坐标 (1101, 299) 处的控件...
[DEBUG]     找到包含坐标的元素: kylin@kylin-pc: ~/kylin-robot-ide (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(797, 119) 大小652x440
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(1101, 299)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 1, 子控件数: 2)
[DEBUG]     检查子控件[0] 'unnamed' (menu bar): 坐标(797, 119) 大小652x30
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(1101, 299)
[DEBUG]     检查子控件[1] 'unnamed' (page tab list): 坐标(797, 149) 大小652x410
[DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(1101, 299)
[DEBUG]     🔍 开始递归搜索子控件[1] 'unnamed' (page tab list)
[DEBUG]     找到包含坐标的元素: unnamed (角色: page tab list, 深度: 2, 子控件数: 1)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: page tab list)
[DEBUG]     检查子控件[0] 'unnamed' (page tab): 坐标(797, 149) 大小0x0
[DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(1101, 299)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: page tab list)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: page tab list, 得分: 1030, 面积: 267320, 分支: 1)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: page tab list)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: page tab list, 得分: 1020, 面积: 267320, 分支: 0)
[DEBUG]     ✅ 选择交互控件: unnamed (角色: page tab list)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: page tab list)
[DEBUG]     向上遍历[0]: unnamed (filler)
[DEBUG]     向上遍历[1]: kylin@kylin-pc: ~/kylin-robot-ide (frame)
[DEBUG]     📋 找到主窗口: kylin@kylin-pc: ~/kylin-robot-ide (frame)
[DEBUG]     向上遍历[2]: mate-terminal (application)
[DEBUG]     向上遍历[3]: main (desktop frame)
[DEBUG]     📋 找到主窗口: main (desktop frame)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (page tab list) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: page tab list)
[DEBUG] ✅ 在应用程序 'mate-terminal' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 111)
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: '文件管理器' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[48]: 'ukui-settings-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='listenhf.py' vs 窗口类名='mate-terminal.mate-terminal'
[DEBUG]   过滤通用标识符: 'mate'
[DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[49]: 'listenHF.py' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: listenHF.py (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'listenHF.py' (得分: 1 < 90)，不进行详细控件查找
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'sni-xembed-proxy', [2] 'ukui-session', [3] 'panel-daemon', [4] 'kglobalaccel'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=111
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (1101, 299) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [page tab list | ]
[UNI] 控件ParentPath: [0, 0, 1]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: mate-terminal
[UNI] ParentPath第一个节点对象: kylin@kylin-pc: ~/kylin-robot-ide (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': 65, 'ProcessID': 26933, 'Rolename': 'page tab list', 'Description': 'N/A', 'Index_in_parent': 1, 'ChildrenCount': 1, 'ProcessName': 'mate-terminal', 'Coords': {'x': 797, 'y': 149, 'width': 652, 'height': 410}, 'Text': 'Not available: ', 'Actions': 'Not available: ', 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 1], 'ParentCount': 3, 'Key': 'NNA-DNA-P001', 'RecordPosition': (1101, 299), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [0, 0, 1]
[UNI] ParentPath第一个节点索引: 0
[UNI] 找到应用程序对象: mate-terminal
[UNI] ParentPath第一个节点对象: kylin@kylin-pc: ~/kylin-robot-ide (角色: frame)
[UNI] 控件实际所属窗口: kylin@kylin-pc: ~/kylin-robot-ide
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': 65, 'ProcessID': 26933, 'Rolename': 'page tab list', 'Description': 'N/A', 'Index_in_parent': 1, 'ChildrenCount': 1, 'ProcessName': 'mate-terminal', 'Coords': {'x': 797, 'y': 149, 'width': 652, 'height': 410}, 'Text': 'Not available: ', 'Actions': 'Not available: ', 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [0, 0, 1], 'ParentCount': 3, 'Key': 'NNA-DNA-P001', 'RecordPosition': (1101, 299), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': 'username@hostname: ~/username-robot-ide'}
[UNI] 控件名称生成为: 标签页列表_(797,149)
[UNI] 控件信息验证通过
[INFO] 控件识别被中断（UNI识别后）
[INFO] 控件识别被中断（初次识别后）
[INFO] 控件识别被中断（悬停超时回调）
[PROCESS] 📥 处理事件: keyboard (剩余队列: 0)
[DEBUG] 准备输出键盘JSON: 按键=c 修饰键=['ctrl']
[DEBUG] 成功输出键盘JSON: 组合键=ctrl+c
[INFO] 正在停止菜单监听进程，PID: 395158
[DEBUG] 进程组ID: 395158
[DEBUG] 发送SIGTERM信号给进程组 395158
[INFO] ✅ 菜单监听进程 395158 已正常结束
[INFO] 菜单监听器状态已重置
[INFO] 录制会话已保存: session_1753721420955
[INFO] 录制时长: 15.05秒
[INFO] 鼠标事件数: 5
[INFO] 键盘事件数: 1

=== 事件统计信息 ===
鼠标事件: 总计5, 重复0, 重复率0.0%
键盘事件: 总计2, 重复0, 重复率0.0%
程序结束
