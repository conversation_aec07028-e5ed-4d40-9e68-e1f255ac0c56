import pyatspi
from gi.repository import GLib
from UNI import UNI
import os
import json
import time

class AppSpecificMenuMonitor:
    """特定应用程序菜单监控器，用于监控和捕获应用程序的弹出菜单"""
    
    def __init__(self):
        # 最后捕获的菜单
        self.last_menu = None
        self.menu_ele = []
        self.menu_ele_now = []
        self.jsondata_bak = None
        self.a = UNI()
        self.recordfile = "/tmp/.recordmenu.txt"
        
        self.last_box = None
        self.box_ele = []
        self.box_ele_now = []
        self.jsondata_bak1 = None
        self.recordfile1 = "/tmp/.recordmenu1.txt"

    def list_available_apps(self):
        """列出系统中所有可用的应用程序
        返回: 包含(应用名称, 进程ID)元组的列表
        """
        desktop = pyatspi.Registry.getDesktop(0)
        available_apps = []
        for app in desktop:
            if app.name:
                available_apps.append((app.name, app.get_process_id()))
        return available_apps

    def find_app(self, app_name):
        """根据应用程序名称查找应用程序实例
        参数:
            app_name: 要查找的应用程序名称
        返回: 找到的应用程序实例，未找到则返回None
        """
        desktop = pyatspi.Registry.getDesktop(0)
        for app in desktop:
            if app.name and app.name.lower() == app_name.lower():
                return app
        return None

    def print_menu_item_info(self, item):
        """打印菜单项的详细信息
        参数:
            item: 要打印信息的菜单项对象
        打印内容包括：名称、角色、描述、状态和位置信息
        """
        print(f"Menu Item: {item.name}")
        print(f"  Role: {item.getRoleName()}")
        print(f"  Description: {item.description}")
        
        states = [pyatspi.stateToString(state) for state in item.getState().getStates()]
        print(f"  States: {', '.join(states)}")
        
        try:
            extents = item.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            print(f"  Position: ({extents.x}, {extents.y})")
            print(f"  Size: {extents.width} x {extents.height}")
        except Exception:
            print("  Position/Size: Not available")
        
        print("  ----")
        
    def menu_ele_record(self, item):
        try:
            extents = item.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            if 0 <= extents.x <= 1980 and 0 <= extents.y <= 1080 and  0 < extents.width <= 1980 and 0 < extents.height <= 1080:
                self.menu_ele_now.append(item)
        except Exception:
            print("  Position/Size: Not available")

    def box_ele_record(self, item):
        try:
            extents = item.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            if 0 <= extents.x <= 1980 and 0 <= extents.y <= 1080 and  0 < extents.width <= 1980 and 0 < extents.height <= 1080:
                self.box_ele_now.append(item)
        except Exception:
            print("  Position/Size: Not available")

    def capture_menu_items(self, menu):
        """递归捕获并打印菜单及其子菜单的所有项目
        参数:
            menu: 要捕获的菜单对象
        """
        # print(f"Capturing items for menu: {menu.name}")
        for i in range(menu.childCount):
            child = menu.getChildAtIndex(i)
            #self.print_menu_item_info(child)
            # 记录当前的elenowlist
            self.menu_ele_record(child)
            if child.childCount > 0:
                self.capture_menu_items(child)
                
    def capture_box_items(self, box):
        """递归捕获并打印菜单及其子菜单的所有项目
        参数:
            menu: 要捕获的菜单对象
        """
        # print(f"Capturing items for menu: {menu.name}")
        for i in range(box.childCount):
            child = box.getChildAtIndex(i)
            #self.print_menu_item_info(child)
            # 记录当前的elenowlist
            self.box_ele_record(child)
            if child.childCount > 0:
                self.capture_box_items(child)
    
    def event_listener(self, event):
        """事件监听器回调函数
        参数:
            event: 接收到的事件对象
        监听所有应用程序的菜单相关事件
        """
        try:
            #print(f"Event from application: {event.source.getApplication().name}")
            #print(f"Event type: {event.type}")

            if (event.type.startswith("object:state-changed:") or
                event.type.startswith("object:children-changed:") or
                event.type.startswith("object:property-change:")):

                if event.source.getRole() in [pyatspi.ROLE_MENU, pyatspi.ROLE_POPUP_MENU, pyatspi.ROLE_MENU_ITEM]:#,pyatspi.ROLE_COMBO_BOX
                    print(f"Popup menu detected in {event.source.getApplication().name}!")
                    self.last_menu = event.source
                    self.menu_ele_now = []
                    self.capture_menu_items(event.source)
                    if self.menu_ele_now:
                        if self.menu_ele_now == self.menu_ele:
                            # 说明是关，则清空
                            self.menu_ele = []
                        else:
                            # 存在任意一个showing控件则替换menu_ele
                            for i in self.menu_ele_now:
                                states = [pyatspi.stateToString(state) for state in i.getState().getStates()]
                                if "showing" in states:
                                    self.menu_ele = self.menu_ele_now
                                    break
                    else:
                        # 说明当前没有menu，置空
                        self.menu_ele = []
                    if self.menu_ele:
                        # 刷jasondata
                        jsondata = {}
                        j = 0
                        for i in self.menu_ele:
                            # 获取元素需要的信息并打印到相关文件中
                            data = self.a._extract_element_info(i)
                            if data["Rolename"] != "popup menu": #or data["Rolename"] != "list":
                                try:
                                    while 1:
                                        if i.getRoleName() == "application":
                                            break
                                        i= i.parent
                                except Exception as e:
                                    pass
                                data["WindowName"] = i.name
                                data["WindowRoleName"] = i.getRoleName()
                                data["WindowChildCount"] = i.childCount
                                print(data)
                                jsondata[j] = data
                                j = j+1
                        if not self.jsondata_bak:
                            os.system(f"> {self.recordfile}")
                            # 控件信息写到文件里
                            with open(self.recordfile, 'a', encoding='UTF-8') as json_file:
                                json.dump(jsondata, json_file, indent=4, ensure_ascii=False)
                            self.jsondata_bak = jsondata
                        elif jsondata == self.jsondata_bak:
                            self.jsondata_bak = jsondata
                        elif self.check_dict_relationship(jsondata, self.jsondata_bak) != 1:
                            os.system(f"> {self.recordfile}")
                            # 控件信息写到文件里
                            with open(self.recordfile, 'a', encoding='UTF-8') as json_file:
                                json.dump(jsondata, json_file, indent=4, ensure_ascii=False)
                            self.jsondata_bak = jsondata
                        elif self.check_dict_relationship(jsondata, self.jsondata_bak) == 1:
                            jsondata = self.replace_coords(self.jsondata_bak, jsondata)
                            os.system(f"> {self.recordfile}")
                            # 控件信息写到文件里
                            with open(self.recordfile, 'a', encoding='UTF-8') as json_file:
                                json.dump(jsondata, json_file, indent=4, ensure_ascii=False)
                    else:
                        if event.source.getRole() in [pyatspi.ROLE_MENU, pyatspi.ROLE_POPUP_MENU]:
                            # 文件清空
                            os.system(f"> {self.recordfile}")
                elif event.source.getRole() in [pyatspi.ROLE_COMBO_BOX]:
                    print(f"Commbobox detected in {event.source.getApplication().name}!")
                    self.last_box = event.source
                    self.box_ele_now = []
                    self.capture_box_items(event.source)
                    if self.box_ele_now:
                        if self.box_ele_now == self.box_ele:
                            # 说明是关，则清空
                            self.box_ele = []
                        else:
                            # 存在任意一个showing控件则替换menu_ele
                            for i in self.box_ele_now:
                                states = [pyatspi.stateToString(state) for state in i.getState().getStates()]
                                if "showing" in states:
                                    self.box_ele = self.box_ele_now
                                    break
                    else:
                        # 说明当前没有menu，置空
                        self.box_ele = []
                    if self.box_ele:
                        # 刷jasondata
                        jsondata1 = {}
                        os.system(f"> {self.recordfile1}")
                        j = 0
                        for i in self.box_ele:
                            # 获取元素需要的信息并打印到相关文件中
                            data = self.a._extract_element_info(i)
                            if data["Rolename"] != "list":
                                try:
                                    while 1:
                                        if i.getRoleName() == "application":
                                            break
                                        i= i.parent
                                except Exception as e:
                                    pass
                                data["WindowName"] = i.name
                                data["WindowRoleName"] = i.getRoleName()
                                data["WindowChildCount"] = i.childCount
                                print(data)
                                jsondata1[j] = data
                                j = j+1
                        with open(self.recordfile1, 'a', encoding='UTF-8') as json_file:
                            json.dump(jsondata1, json_file, indent=4, ensure_ascii=False)
                    else:
                        os.system(f"> {self.recordfile1}")
        except Exception as e:
            print(f"Error in event listener: {e}")
            
    def replace_coords(self, a, b):
        # 创建一个从Name到b中对应项的映射
        name_to_b = {item['Name']: item for item in b.values() if 'Name' in item and 'Coords' in item}
    
        # 遍历a中的每个项
        for key, item_a in a.items():
            if 'Name' in item_a and item_a['Name'] in name_to_b:
                # 获取b中对应Name的项
                item_b = name_to_b[item_a['Name']]
                # 确保b的项中有Coords且为字典
                if 'Coords' in item_b and isinstance(item_b['Coords'], dict):
                    # 获取b中的x和y
                    x_b = item_b['Coords'].get('x')
                    y_b = item_b['Coords'].get('y')
                    # 确保a的项中有Coords且为字典
                    if 'Coords' in item_a and isinstance(item_a['Coords'], dict):
                        # 替换a中的x和y
                        if x_b is not None:
                            item_a['Coords']['x'] = x_b
                        if y_b is not None:
                            item_a['Coords']['y'] = y_b
        return a
    
    
    def process_subdict(self, subdict):
        """预处理子字典：移除Coords中的x和y，保留其他字段"""
        # 复制子字典避免修改原数据
        processed = subdict.copy()
        # 处理Coords字段（如果存在）
        if 'Coords' in processed and isinstance(processed['Coords'], dict):
            coords = processed['Coords'].copy()  # 只对字典类型执行copy()
            coords.pop('x', None)  # 移除x
            coords.pop('y', None)  # 移除y
            processed['Coords'] = coords
        # 若Coords不是字典（如字符串），不做处理
        return processed

    def is_subdict_contained(self, processed_a, processed_b):
        """判断预处理后的子字典processed_a是否被processed_b包含（所有键值对均匹配）"""
        for key, value in processed_a.items():
            # 键不存在，或值不匹配
            if key not in processed_b:
                return False
            # 如果值是字典（如Coords），递归检查
            if isinstance(value, dict) and isinstance(processed_b[key], dict):
                if not self.is_subdict_contained(value, processed_b[key]):
                    return False
            # 非字典值直接比较
            elif value != processed_b[key]:
                return False
        return True

    def check_dict_relationship(self, a, b):
        """检查两个字典的包含关系（忽略第一层key，忽略Coords的x和y）"""
        # 提取a和b的所有子字典（忽略第一层key）
        a_subdicts = list(a.values())
        b_subdicts = list(b.values())
    
        # 预处理所有子字典（移除Coords的x和y）
        a_processed = [self.process_subdict(sd) for sd in a_subdicts]
        b_processed = [self.process_subdict(sd) for sd in b_subdicts]
    
        # 检查a的所有子字典是否都被b包含
        a_contained_in_b = all(
            any(self.is_subdict_contained(pa, pb) for pb in b_processed)
            for pa in a_processed
        )
    
        # 检查b的所有子字典是否都被a包含
        b_contained_in_a = all(
            any(self.is_subdict_contained(pb, pa) for pa in a_processed)
            for pb in b_processed
        )
    
        # 返回最终关系
        if a_contained_in_b and b_contained_in_a:
            return 0 #"字典a和字典b的子字典完全互相包含"
        elif a_contained_in_b:
            return 1 #"字典a的所有子字典均被字典b包含"
        elif b_contained_in_a:
            return 2 #"字典b的所有子字典均被字典a包含"
        else:
            return 3 #"字典a和字典b的子字典无包含关系"
            
    def start_monitoring(self):
        """开始监控所有应用程序的菜单"""
        print("Starting popup menu monitoring for all applications...")
        
        pyatspi.Registry.registerEventListener(self.event_listener, "object:state-changed")
        pyatspi.Registry.registerEventListener(self.event_listener, "object:children-changed")
        pyatspi.Registry.registerEventListener(self.event_listener, "object:property-change")
        
        try:
            pyatspi.Registry.start()
        except KeyboardInterrupt:
            print("Monitoring stopped by user.")
        finally:
            pyatspi.Registry.deregisterEventListener(self.event_listener, "object:state-changed")
            pyatspi.Registry.deregisterEventListener(self.event_listener, "object:children-changed")
            pyatspi.Registry.deregisterEventListener(self.event_listener, "object:property-change")

# 主程序入口
if __name__ == "__main__":
    """
    主程序流程：
    1. 创建监控器实例
    2. 开始监控所有应用程序的菜单
    """
    monitor = AppSpecificMenuMonitor()
    print("Starting to monitor menus for all applications...")
    print("Press Ctrl+C to stop monitoring.")
    monitor.start_monitoring()
