#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于左上角的偏移量计算功能
作者: AI助手
日期: 2024.12.17
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auto_recording_manager import AutoRecordingManager

def test_topleft_offset_calculation():
    """
    测试基于左上角的偏移量计算功能
    """
    print("🧪 测试基于左上角的偏移量计算功能")
    print("=" * 80)
    
    # 创建录制管理器实例
    manager = AutoRecordingManager(debug=True)
    
    # 测试用例1: 左上角点击
    print("\n📍 测试1: 左上角点击")
    print("-" * 50)
    
    # 模拟一个控件：左上角(100, 50)，大小200x100
    coords = {
        'left': 100,
        'top': 50,
        'width': 200,
        'height': 100
    }
    
    # 点击左上角
    topleft_x = 100  # 控件左上角X坐标
    topleft_y = 50   # 控件左上角Y坐标
    
    result = manager._calculate_relative_offset_percentage(topleft_x, topleft_y, coords)
    print(f"控件区域: ({coords['left']}, {coords['top']}, {coords['width']}, {coords['height']})")
    print(f"点击位置: ({topleft_x}, {topleft_y})")
    print(f"期望结果: 偏移(0%, 0%), 距左上角0%")
    print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距左上角{result['distance_from_topleft_percent']:.1f}%")
    
    # 验证结果
    if abs(result['offset_x_percent']) < 0.1 and abs(result['offset_y_percent']) < 0.1:
        print("✅ 左上角点击测试通过")
    else:
        print("❌ 左上角点击测试失败")

    # 测试用例2: 中心点击
    print("\n📍 测试2: 中心点击")
    print("-" * 50)
    center_x = 100 + 200/2  # 200
    center_y = 50 + 100/2   # 100
    
    result = manager._calculate_relative_offset_percentage(center_x, center_y, coords)
    print(f"点击位置: ({center_x}, {center_y})")
    print(f"期望结果: 偏移(50%, 50%), 距左上角约70.7%")
    print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距左上角{result['distance_from_topleft_percent']:.1f}%")
    
    # 验证结果（中心点相对于左上角的偏移应该是50%）
    if abs(result['offset_x_percent'] - 50) < 1 and abs(result['offset_y_percent'] - 50) < 1:
        print("✅ 中心点击测试通过")
    else:
        print("❌ 中心点击测试失败")

    # 测试用例3: 右下角点击
    print("\n📍 测试3: 右下角点击")
    print("-" * 50)
    corner_x = 300  # 右边界 (100 + 200)
    corner_y = 150  # 下边界 (50 + 100)
    
    result = manager._calculate_relative_offset_percentage(corner_x, corner_y, coords)
    print(f"点击位置: ({corner_x}, {corner_y})")
    print(f"期望结果: 偏移(100%, 100%), 距左上角约141.4%")
    print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距左上角{result['distance_from_topleft_percent']:.1f}%")
    
    if abs(result['offset_x_percent'] - 100) < 1 and abs(result['offset_y_percent'] - 100) < 1:
        print("✅ 右下角点击测试通过")
    else:
        print("❌ 右下角点击测试失败")

    # 测试用例4: 控件外点击
    print("\n📍 测试4: 控件外点击")
    print("-" * 50)
    outside_x = 350  # 超出右边界
    outside_y = 200  # 超出下边界
    
    result = manager._calculate_relative_offset_percentage(outside_x, outside_y, coords)
    print(f"点击位置: ({outside_x}, {outside_y})")
    print(f"期望结果: 偏移(125%, 150%), 距左上角约194.9%")
    print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距左上角{result['distance_from_topleft_percent']:.1f}%")
    print("🔍 控件外点击也能正常计算偏移百分比")

    # 测试用例5: 实际控件场景模拟 
    print("\n📍 测试5: 实际控件场景模拟")
    print("-" * 50)
    
    # 模拟一个实际的按钮控件
    button_coords = {
        'left': 552,
        'top': 224,
        'width': 80,
        'height': 30
    }
    
    # 模拟点击按钮的不同位置
    test_points = [
        (552, 224, "左上角点击"),       # 左上角: (552, 224)
        (592, 239, "中心点击"),        # 中心: (552+40, 224+15)
        (632, 254, "右下角点击"),      # 右下角: (552+80, 224+30)
        (572, 234, "左上偏移"),       # 左上偏移
        (612, 244, "右下偏移"),       # 右下偏移
    ]
    
    for x, y, description in test_points:
        result = manager._calculate_relative_offset_percentage(x, y, button_coords)
        print(f"  {description}: 点击({x}, {y}) -> "
              f"偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%) "
              f"距左上角{result['distance_from_topleft_percent']:.1f}%")

    # 测试用例6: 边界值测试
    print("\n📍 测试6: 边界值测试")
    print("-" * 50)
    
    # 测试零尺寸控件
    zero_coords = {'left': 100, 'top': 50, 'width': 0, 'height': 0}
    result = manager._calculate_relative_offset_percentage(100, 50, zero_coords)
    print(f"零尺寸控件: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%) "
          f"距左上角{result['distance_from_topleft_percent']:.1f}%")
    
    # 测试空坐标
    result = manager._calculate_relative_offset_percentage(100, 50, {})
    print(f"空坐标: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%) "
          f"距左上角{result['distance_from_topleft_percent']:.1f}%")
    
    # 测试None坐标
    result = manager._calculate_relative_offset_percentage(100, 50, None)
    print(f"None坐标: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%) "
          f"距左上角{result['distance_from_topleft_percent']:.1f}%")

    print("\n" + "=" * 80)
    print("🎉 基于左上角的偏移量计算测试完成！")
    print("\n💡 新的偏移量计算逻辑说明:")
    print("   • 偏移量基点：控件左上角坐标")
    print("   • X偏移：0%=左边界，100%=右边界")
    print("   • Y偏移：0%=上边界，100%=下边界")
    print("   • 距离：相对于左上角的欧几里得距离百分比")

def main():
    """主函数"""
    try:
        test_topleft_offset_calculation()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
