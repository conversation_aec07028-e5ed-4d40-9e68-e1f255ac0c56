#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UNI模块基本功能测试
作者: AI助手
日期: 2024.12.17
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_uni_import():
    """测试UNI模块导入"""
    try:
        from UNI import UNI
        print("✅ UNI模块导入成功")
        return UNI
    except ImportError as e:
        print(f"❌ UNI模块导入失败: {e}")
        return None
    except Exception as e:
        print(f"❌ UNI模块导入异常: {e}")
        return None

def test_uni_instance():
    """测试UNI实例创建"""
    try:
        UNI_class = test_uni_import()
        if not UNI_class:
            return None
        
        uni = UNI_class()
        print("✅ UNI实例创建成功")
        return uni
    except Exception as e:
        print(f"❌ UNI实例创建失败: {e}")
        return None

def test_basic_method():
    """测试基本方法调用"""
    try:
        uni = test_uni_instance()
        if not uni:
            return False
        
        # 测试基本的控件识别方法
        result, msg = uni.kdk_getElement_Uni(100, 100)
        print("✅ kdk_getElement_Uni方法调用成功")
        print(f"   结果类型: {type(result)}")
        print(f"   消息: {msg}")
        return True
    except Exception as e:
        print(f"❌ 基本方法调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 UNI模块基本功能测试")
    print("=" * 50)
    
    print("\n1. 导入测试")
    print("-" * 20)
    if not test_uni_import():
        print("❌ 导入测试失败，退出")
        return
    
    print("\n2. 实例创建测试")
    print("-" * 20)
    if not test_uni_instance():
        print("❌ 实例创建测试失败，退出")
        return
    
    print("\n3. 基本方法测试")
    print("-" * 20)
    if not test_basic_method():
        print("❌ 基本方法测试失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！UNI模块工作正常")

if __name__ == "__main__":
    main()
