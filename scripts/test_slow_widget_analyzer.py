#!/usr/bin/env python3
"""
模拟慢速控件分析器，用于测试超时重启功能
"""

import sys
import time
import os
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入原始的auto_recording_manager
from auto_recording_manager import AutoRecordingManager, HoverDetector

class SlowWidgetAnalyzer:
    """模拟慢速的控件分析器"""
    
    def __init__(self, debug=False, delay_seconds=5):
        self.debug = debug
        self.delay_seconds = delay_seconds
        print(f"[SLOW] 慢速控件分析器初始化，延迟: {delay_seconds}秒", file=sys.stderr)
    
    def analyze_widget_at_with_new_app_detection(self, x, y, interrupt_flag=None):
        """模拟耗时很长的控件识别"""
        print(f"[SLOW] 🐌 开始慢速控件识别... 位置=({x}, {y})", file=sys.stderr)
        print(f"[SLOW] 🐌 将阻塞 {self.delay_seconds} 秒来模拟长时间识别", file=sys.stderr)
        
        # 模拟长时间阻塞
        for i in range(self.delay_seconds):
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[SLOW] 🛑 控件识别被中断", file=sys.stderr)
                return None, "识别被中断"
            
            time.sleep(1)
            print(f"[SLOW] 🐌 识别进行中... {i+1}/{self.delay_seconds}秒", file=sys.stderr)
        
        print(f"[SLOW] ✅ 慢速控件识别完成", file=sys.stderr)
        return {
            'Name': 'SlowWidget',
            'Rolename': 'button',
            'ProcessName': 'test_app',
            'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
        }, "慢速识别成功"
    
    def analyze_widget_at(self, x, y, interrupt_flag=None):
        """模拟耗时很长的控件识别（简化版）"""
        print(f"[SLOW] 🐌 开始慢速控件识别(简化版)... 位置=({x}, {y})", file=sys.stderr)
        print(f"[SLOW] 🐌 将阻塞 {self.delay_seconds} 秒来模拟长时间识别", file=sys.stderr)
        
        # 模拟长时间阻塞
        for i in range(self.delay_seconds):
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[SLOW] 🛑 控件识别被中断(简化版)", file=sys.stderr)
                return None, "识别被中断"
            
            time.sleep(1)
            print(f"[SLOW] 🐌 识别进行中(简化版)... {i+1}/{self.delay_seconds}秒", file=sys.stderr)
        
        print(f"[SLOW] ✅ 慢速控件识别完成(简化版)", file=sys.stderr)
        return {
            'Name': 'SlowWidget',
            'Rolename': 'button',
            'ProcessName': 'test_app',
            'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
        }, "慢速识别成功"

def create_slow_auto_recording_manager(delay_seconds=5):
    """创建使用慢速控件分析器的录制管理器"""
    
    # 创建慢速控件分析器
    slow_analyzer = SlowWidgetAnalyzer(debug=True, delay_seconds=delay_seconds)
    
    # 创建录制管理器
    manager = AutoRecordingManager(debug=True, json_output=True)
    
    # 替换控件分析器
    manager.widget_analyzer = slow_analyzer
    
    # 替换悬停检测器中的控件分析器
    if hasattr(manager, 'hover_detector') and manager.hover_detector:
        manager.hover_detector.widget_analyzer = slow_analyzer
    
    return manager

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='慢速控件分析器测试')
    parser.add_argument('--delay', type=int, default=5, help='控件识别延迟时间（秒），默认5')
    parser.add_argument('--duration', type=int, default=60, help='运行时长（秒），默认60')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    print(f"[MAIN] 🧪 启动慢速控件分析器测试", file=sys.stderr)
    print(f"[MAIN] 📊 控件识别延迟: {args.delay}秒", file=sys.stderr)
    print(f"[MAIN] 📊 运行时长: {args.duration}秒", file=sys.stderr)
    print(f"[MAIN] 📊 调试模式: {args.debug}", file=sys.stderr)
    print("=" * 60, file=sys.stderr)
    
    try:
        # 创建慢速录制管理器
        manager = create_slow_auto_recording_manager(delay_seconds=args.delay)
        
        # 启动录制
        print(f"[MAIN] 🚀 启动录制管理器...", file=sys.stderr)
        manager.start_recording(duration=args.duration)
        
        # 等待录制完成
        start_time = time.time()
        while manager.is_recording and (time.time() - start_time) < args.duration:
            time.sleep(1)
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0:  # 每10秒报告一次
                print(f"[MAIN] ⏱️ 录制进行中... {elapsed:.0f}/{args.duration}秒", file=sys.stderr)
        
        # 停止录制
        if manager.is_recording:
            print(f"[MAIN] 🛑 停止录制...", file=sys.stderr)
            manager.stop_recording()
        
        # 清理
        manager.cleanup()
        
        print(f"[MAIN] ✅ 测试完成", file=sys.stderr)
        
    except KeyboardInterrupt:
        print(f"\n[MAIN] 🛑 用户中断", file=sys.stderr)
        if 'manager' in locals():
            manager.cleanup()
    except Exception as e:
        print(f"[ERROR] 测试异常: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        if 'manager' in locals():
            manager.cleanup()

if __name__ == "__main__":
    main()
