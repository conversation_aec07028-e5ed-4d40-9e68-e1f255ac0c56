#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控件高亮捕获模块
实现控件高亮捕获功能，用于在屏幕上高亮显示并捕获控件信息
"""

import sys
import time
import threading
import json
import signal
from typing import Dict, Any, Optional, Callable, Tuple

try:
    from Xlib import X, display, Xutil, Xatom
    XLIB_AVAILABLE = True
except ImportError:
    XLIB_AVAILABLE = False
    print("[ERROR] Xlib库未安装，请使用 pip install python-xlib 安装", file=sys.stderr)

try:
    from pynput import keyboard, mouse
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("[WARNING] pynput库未安装，请使用 pip install pynput 安装", file=sys.stderr)

# 导入UNI模块
try:
    # 尝试多个可能的路径
    UNI_PATHS = [
        "scripts",  # 主要路径
        "extensions/KylinRobot-v2/common",  # 备用路径1
        ".",  # 当前目录
    ]

    UNI_AVAILABLE = False
    for path in UNI_PATHS:
        try:
            if path not in sys.path:
                sys.path.insert(0, path)
            from UNI import UNI
            UNI_AVAILABLE = True
            print(f"[INFO] UNI模块从路径加载成功: {path}", file=sys.stderr)
            break
        except ImportError:
            continue

    if not UNI_AVAILABLE:
        print("[ERROR] UNI模块导入失败，尝试的路径:", file=sys.stderr)
        for path in UNI_PATHS:
            print(f"  - {path}", file=sys.stderr)

except Exception as e:
    UNI_AVAILABLE = False
    print(f"[ERROR] UNI模块导入异常: {e}", file=sys.stderr)


class UNIWidgetFinder:
    """
    UNI控件查找器
    负责调用UNI.py中的kdk_getElement_Uni函数获取控件信息
    """
    def __init__(self, debug=False):
        self.debug = debug
        if UNI_AVAILABLE:
            self.uni = UNI()
        else:
            self.uni = None
            print("[ERROR] UNI模块不可用，无法获取控件信息", file=sys.stderr)

    def find_widget_at(self, x: int, y: int) -> Tuple[Optional[Dict[str, Any]], str]:
        """在指定坐标查找控件"""
        if self.debug:
            print(f"[DEBUG] 查找坐标({x}, {y})处的控件", file=sys.stderr)

        if not self.uni:
            print("[ERROR] UNI模块不可用，无法获取控件信息", file=sys.stderr)
            return None, "UNI模块不可用"

        try:
            # 调用UNI.py的kdk_getElement_Uni函数获取控件信息，启用菜单控件识别
            widget_info, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)

            if self.debug:
                if widget_info:
                    print(f"[DEBUG] 找到控件: {widget_info.get('Name', 'Unknown')}", file=sys.stderr)
                    if 'Coords' in widget_info:
                        coords = widget_info['Coords']
                        print(f"[DEBUG] 控件坐标: x={coords['x']}, y={coords['y']}, width={coords['width']}, height={coords['height']}", file=sys.stderr)
                else:
                    print(f"[DEBUG] 未找到控件: {info_text}", file=sys.stderr)

            # 只返回真实的控件信息，不使用模拟数据
            return widget_info, info_text
        except Exception as e:
            error_msg = f"查找控件时发生错误: {e}"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            return None, error_msg


class HighlightRenderer:
    """
    高亮渲染器
    负责在屏幕上绘制高亮边框和控件类别文字
    """
    def __init__(self, debug=False):
        self.debug = debug

        # 初始化所有属性
        self.display = None
        self.screen = None
        self.root = None
        self.border_width = 3
        self.border_color = None
        self.top_border = None
        self.bottom_border = None
        self.left_border = None
        self.right_border = None
        self.text_label = None
        self.text_gc = None
        self.highlight_visible = False
        self.highlight_x = 0
        self.highlight_y = 0
        self.highlight_width = 100
        self.highlight_height = 100
        self.last_update_time = 0
        self.update_throttle = 0.05  # 50ms
        self.current_highlight_info = None

        if not XLIB_AVAILABLE:
            print("[ERROR] Xlib库不可用，无法创建高亮窗口", file=sys.stderr)
            return

        try:
            # 初始化Xlib显示和根窗口
            self.display = display.Display()
            self.screen = self.display.screen()
            self.root = self.screen.root

            # 创建高亮窗口
            self.create_highlight_windows()

            if self.debug:
                print("[DEBUG] 高亮渲染器初始化完成", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 初始化高亮渲染器时发生错误: {e}", file=sys.stderr)
            # 确保在失败时所有属性都有默认值
            self.display = None

    def create_highlight_windows(self):
        """创建高亮窗口（只有四个边框）"""
        if self.debug:
            print("[DEBUG] 创建高亮窗口", file=sys.stderr)

        try:
            # 先分配红色
            colormap = self.screen.default_colormap
            try:
                red_color = colormap.alloc_named_color("red")
                background_pixel = red_color.pixel
                if self.debug:
                    print(f"[DEBUG] 使用红色作为背景: pixel={background_pixel}", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] 红色分配失败，使用白色: {e}", file=sys.stderr)
                background_pixel = self.screen.white_pixel

            # 创建四个边框窗口
            # 上边框
            self.top_border = self.root.create_window(
                0, 0,  # 初始位置
                self.highlight_width, self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=background_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 下边框
            self.bottom_border = self.root.create_window(
                0, self.highlight_height - self.border_width,  # 初始位置
                self.highlight_width, self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=background_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 左边框
            self.left_border = self.root.create_window(
                0, self.border_width,  # 初始位置
                self.border_width, self.highlight_height - 2 * self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=background_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 右边框
            self.right_border = self.root.create_window(
                self.highlight_width - self.border_width, self.border_width,  # 初始位置
                self.border_width, self.highlight_height - 2 * self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=background_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 创建文字标签窗口
            self.text_label = self.root.create_window(
                0, 0,  # 初始位置
                200, 30,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=self.screen.white_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 设置窗口属性
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border, self.text_label]:
                # 设置窗口类型为工具窗口
                window.change_property(
                    self.display.intern_atom('_NET_WM_WINDOW_TYPE'),
                    Xatom.ATOM,
                    32,
                    [self.display.intern_atom('_NET_WM_WINDOW_TYPE_UTILITY')]
                )

                # 设置窗口状态为总是在最上层
                window.change_property(
                    self.display.intern_atom('_NET_WM_STATE'),
                    Xatom.ATOM,
                    32,
                    [self.display.intern_atom('_NET_WM_STATE_ABOVE')]
                )

            # 设置红色
            try:
                colormap = self.screen.default_colormap
                self.border_color = colormap.alloc_named_color("red")

                if self.debug:
                    print(f"[DEBUG] 红色分配成功: pixel={self.border_color.pixel}", file=sys.stderr)

                # 设置所有边框为红色
                for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                    window.change_attributes(background_pixel=self.border_color.pixel)
                    if self.debug:
                        print(f"[DEBUG] 窗口 {window.id} 背景色已设置为红色", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 设置红色失败: {e}", file=sys.stderr)
                # 如果红色设置失败，使用白色作为备选
                try:
                    for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                        window.change_attributes(background_pixel=self.screen.white_pixel)
                    print(f"[WARNING] 使用白色作为高亮颜色", file=sys.stderr)
                except Exception as e2:
                    print(f"[ERROR] 设置备选颜色也失败: {e2}", file=sys.stderr)

            # 创建文字绘制的图形上下文
            try:
                # 分配颜色 - 使用红色文字，白色背景
                colormap = self.screen.default_colormap
                red_color = colormap.alloc_named_color("red")
                white_color = colormap.alloc_named_color("white")

                self.text_gc = self.text_label.create_gc(
                    foreground=red_color.pixel,
                    background=white_color.pixel
                )

                # 尝试设置字体（支持中文）
                try:
                    # 尝试使用支持中文的字体
                    font_names = [
                        "-*-*-medium-r-*-*-14-*-*-*-*-*-iso10646-1",  # Unicode字体
                        "-*-*-medium-r-*-*-12-*-*-*-*-*-*-*",         # 默认字体
                        "fixed"                                        # 备用字体
                    ]

                    for font_name in font_names:
                        try:
                            font = self.display.open_font(font_name)
                            self.text_gc.change(font=font)
                            if self.debug:
                                print(f"[DEBUG] 成功设置字体: {font_name}", file=sys.stderr)
                            break
                        except:
                            continue
                    else:
                        if self.debug:
                            print("[DEBUG] 使用默认字体", file=sys.stderr)

                except Exception as font_e:
                    if self.debug:
                        print(f"[DEBUG] 字体设置失败，使用默认字体: {font_e}", file=sys.stderr)

                if self.debug:
                    print("[DEBUG] 文字绘制图形上下文创建成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 创建文字绘制图形上下文失败: {e}", file=sys.stderr)
                self.text_gc = None

            # 初始状态不显示高亮窗口
            self.clear_highlight()

            return True
        except Exception as e:
            print(f"[ERROR] 创建高亮窗口时发生错误: {e}", file=sys.stderr)
            return False

    def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """高亮显示控件并显示控件类别文字"""
        if not XLIB_AVAILABLE or not self.display:
            if self.debug:
                print("[DEBUG] Xlib不可用或显示未初始化，跳过高亮显示", file=sys.stderr)
            return False

        if not all([self.top_border, self.bottom_border, self.left_border, self.right_border]):
            if self.debug:
                print("[DEBUG] 高亮窗口未创建，跳过高亮显示", file=sys.stderr)
            return False

        # 节流处理，避免频繁更新导致闪烁
        current_time = time.time()
        if current_time - self.last_update_time < self.update_throttle:
            return True
        self.last_update_time = current_time

        # 检查是否与当前高亮区域相同，如果相同则不需要更新
        if (self.highlight_visible and
            self.highlight_x == x and
            self.highlight_y == y and
            self.highlight_width == width and
            self.highlight_height == height):
            return True

        try:
            # 更新高亮位置和大小
            self.highlight_x = x
            self.highlight_y = y
            self.highlight_width = width
            self.highlight_height = height

            # 更新边框位置
            # 上边框
            self.top_border.configure(
                x=x,
                y=y,
                width=width,
                height=self.border_width
            )

            # 下边框
            self.bottom_border.configure(
                x=x,
                y=y + height - self.border_width,
                width=width,
                height=self.border_width
            )

            # 左边框
            self.left_border.configure(
                x=x,
                y=y + self.border_width,
                width=self.border_width,
                height=height - 2 * self.border_width
            )

            # 右边框
            self.right_border.configure(
                x=x + width - self.border_width,
                y=y + self.border_width,
                width=self.border_width,
                height=height - 2 * self.border_width
            )

            # 显示所有边框
            for i, window in enumerate([self.top_border, self.bottom_border, self.left_border, self.right_border]):
                window.map()
                if self.debug:
                    border_names = ["上", "下", "左", "右"]
                    print(f"[DEBUG] 显示{border_names[i]}边框窗口: {window.id}", file=sys.stderr)

            # 显示控件类别文字标签
            self._show_widget_label(x, y, width, height, widget_info)

            # 强制刷新显示
            self.display.flush()
            self.display.sync()  # 添加同步调用确保立即显示
            self.highlight_visible = True

            if self.debug:
                print(f"[DEBUG] ✅ 高亮控件完成: x={x}, y={y}, width={width}, height={height}", file=sys.stderr)
                print(f"[DEBUG] 边框宽度: {self.border_width}, 高亮状态: {self.highlight_visible}", file=sys.stderr)

            return True
        except Exception as e:
            print(f"[ERROR] 高亮控件时发生错误: {e}", file=sys.stderr)
            return False

    def _show_widget_label(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """显示控件类别文字标签"""
        if not self.text_label or not self.text_gc:
            return

        try:
            # 生成显示文字
            label_text = self._generate_label_text(widget_info)
            if not label_text:
                return

            # 计算文字标签位置（显示在控件右上角）
            label_x = x + width + 5  # 控件右边5像素处
            label_y = y - 5  # 控件上方5像素处

            # 确保标签不超出屏幕边界
            screen_width = self.screen.width_in_pixels
            screen_height = self.screen.height_in_pixels

            label_width = len(label_text) * 8 + 10  # 估算文字宽度
            label_height = 25

            if label_x + label_width > screen_width:
                label_x = x - label_width - 5  # 显示在控件左边
            if label_y < 0:
                label_y = y + height + 5  # 显示在控件下方

            # 调整文字标签窗口位置和大小
            self.text_label.configure(
                x=label_x,
                y=label_y,
                width=label_width,
                height=label_height
            )

            # 显示文字标签窗口
            self.text_label.map()

            # 等待窗口映射完成
            self.display.sync()

            # 绘制文字 - 使用Xlib的正确方法
            self._draw_text_on_window(self.text_label, label_text, 5, 18)

            if self.debug:
                print(f"[DEBUG] 显示控件标签: '{label_text}' 位置=({label_x}, {label_y})", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 显示控件标签失败: {e}", file=sys.stderr)

    def _draw_text_on_window(self, window, text: str, x: int, y: int):
        """在窗口上绘制红色英文文字"""
        if not self.text_gc or not window:
            return

        try:
            # 直接绘制英文文字
            self._try_draw_text(window, text, x, y)

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 文字绘制异常: {e}", file=sys.stderr)

    def _try_draw_text(self, window, text: str, x: int, y: int) -> bool:
        """绘制红色英文文字"""
        if not text:
            return False

        try:
            # 使用ASCII编码绘制英文文字
            text_bytes = text.encode('ascii', 'ignore')
            window.image_text(self.text_gc, x, y, text_bytes)
            self.display.flush()

            if self.debug:
                print(f"[DEBUG] 红色文字绘制成功: '{text}'", file=sys.stderr)
            return True

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 文字绘制失败: {e}", file=sys.stderr)
            return False



    def _generate_label_text(self, widget_info: dict = None) -> str:
        """生成控件标签文字 - 直接使用英文显示控件类型"""
        if not widget_info:
            return ""

        try:
            # 获取控件角色类型
            role = widget_info.get('Rolename', widget_info.get('Role', ''))

            # 角色类型英文映射
            role_translations = {
                'push button': 'Button',
                'text': 'Text',
                'label': 'Label',
                'entry': 'Entry',
                'menu': 'Menu',
                'menu item': 'MenuItem',
                'window': 'Window',
                'frame': 'Frame',
                'panel': 'Panel',
                'list': 'List',
                'list item': 'ListItem',
                'table': 'Table',
                'table cell': 'Cell',
                'combo box': 'ComboBox',
                'check box': 'CheckBox',
                'radio button': 'RadioButton',
                'scroll bar': 'ScrollBar',
                'tool bar': 'ToolBar',
                'status bar': 'StatusBar',
                'tree': 'Tree',
                'tree item': 'TreeItem',
                'tab': 'Tab',
                'tab list': 'TabList',
                'dialog': 'Dialog',
                'alert': 'Alert',
                'separator': 'Separator',
                'link': 'Link',
                'image': 'Image',
                'icon': 'Icon',
                'progress bar': 'ProgressBar',
                'slider': 'Slider',
                'spin button': 'SpinButton',
                'toggle button': 'ToggleButton',
                'unknown': 'Unknown'
            }

            # 直接返回英文控件类型
            return role_translations.get(role.lower(), role.title() if role else 'Widget')

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 生成标签文字失败: {e}", file=sys.stderr)
            return "Widget"

    def clear_highlight(self):
        """清除高亮"""
        if not XLIB_AVAILABLE or not self.display:
            return

        if not self.highlight_visible:
            return

        try:
            # 隐藏所有边框
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                if window:
                    window.unmap()

            # 隐藏文字标签
            if self.text_label:
                self.text_label.unmap()

            # 强制刷新显示
            self.display.flush()
            self.highlight_visible = False

            if self.debug:
                print("[DEBUG] 清除高亮", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清除高亮时发生错误: {e}", file=sys.stderr)

    def cleanup(self):
        """清理资源"""
        if not XLIB_AVAILABLE or not self.display:
            return

        try:
            # 销毁所有窗口
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                if window:
                    window.destroy()

            # 销毁文字标签窗口
            if self.text_label:
                self.text_label.destroy()

            # 刷新显示
            self.display.flush()

            if self.debug:
                print("[DEBUG] 高亮渲染器资源已清理", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清理高亮渲染器资源时发生错误: {e}", file=sys.stderr)


class KeyboardEventMonitor:
    """
    键盘事件监控器
    负责监控键盘事件，特别是Ctrl键的按下和释放
    """
    def __init__(self, manager, debug=False):
        self.manager = manager
        self.debug = debug
        self.listener = None

    def start(self):
        """开始监控键盘事件"""
        if not PYNPUT_AVAILABLE:
            print("[ERROR] pynput库不可用，无法监控键盘事件", file=sys.stderr)
            return False

        if self.listener:
            return True

        try:
            # 使用pynput监控全局键盘事件
            self.listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.listener.daemon = True
            self.listener.start()

            if self.debug:
                print("[DEBUG] 键盘事件监控已启动", file=sys.stderr)

            return True
        except Exception as e:
            print(f"[ERROR] 启动键盘事件监控时发生错误: {e}", file=sys.stderr)
            return False

    def stop(self):
        """停止监控键盘事件"""
        if not self.listener:
            return

        try:
            self.listener.stop()
            self.listener = None

            if self.debug:
                print("[DEBUG] 键盘事件监控已停止", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 停止键盘事件监控时发生错误: {e}", file=sys.stderr)

    def on_key_press(self, key):
        """处理键盘按下事件"""
        try:
            if key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                if self.debug:
                    print("[DEBUG] Ctrl键按下", file=sys.stderr)

                self.manager.on_ctrl_pressed()
            elif key == keyboard.Key.esc:
                if self.debug:
                    print("[DEBUG] ESC键按下，取消捕获", file=sys.stderr)

                self.manager.stop_capture()
        except Exception as e:
            print(f"[ERROR] 处理键盘按下事件时发生错误: {e}", file=sys.stderr)

    def on_key_release(self, key):
        """处理键盘释放事件"""
        try:
            if key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                if self.debug:
                    print("[DEBUG] Ctrl键释放", file=sys.stderr)

                self.manager.on_ctrl_released()
        except Exception as e:
            print(f"[ERROR] 处理键盘释放事件时发生错误: {e}", file=sys.stderr)


class MouseEventMonitor:
    """
    鼠标事件监控器
    负责监控鼠标事件，包括移动和点击
    """
    def __init__(self, manager, debug=False):
        self.manager = manager
        self.debug = debug
        self.listener = None

    def start(self):
        """开始监控鼠标事件"""
        if not PYNPUT_AVAILABLE:
            print("[ERROR] pynput库不可用，无法监控鼠标事件", file=sys.stderr)
            return False

        if self.listener:
            return True

        try:
            # 使用pynput监控全局鼠标事件
            self.listener = mouse.Listener(
                on_move=self.on_mouse_move,
                on_click=self.on_mouse_click
            )
            self.listener.daemon = True
            self.listener.start()

            if self.debug:
                print("[DEBUG] 鼠标事件监控已启动", file=sys.stderr)

            return True
        except Exception as e:
            print(f"[ERROR] 启动鼠标事件监控时发生错误: {e}", file=sys.stderr)
            return False

    def stop(self):
        """停止监控鼠标事件"""
        if not self.listener:
            return

        try:
            self.listener.stop()
            self.listener = None

            if self.debug:
                print("[DEBUG] 鼠标事件监控已停止", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 停止鼠标事件监控时发生错误: {e}", file=sys.stderr)

    def on_mouse_move(self, x, y):
        """处理鼠标移动事件"""
        try:
            self.manager.on_mouse_move(x, y)
        except Exception as e:
            print(f"[ERROR] 处理鼠标移动事件时发生错误: {e}", file=sys.stderr)

    def on_mouse_click(self, x, y, button, pressed):
        """处理鼠标点击事件"""
        try:
            # 只处理鼠标按下事件
            if pressed and button == mouse.Button.left:
                if self.debug:
                    print(f"[DEBUG] 鼠标左键点击: ({x}, {y}), Ctrl键状态={self.manager.ctrl_pressed}", file=sys.stderr)

                # 检查是否是Ctrl+鼠标点击
                if self.manager.ctrl_pressed:
                    # 检查点击是否在高亮区域内
                    if self.is_point_in_highlight(x, y):
                        if self.debug:
                            print(f"[DEBUG] Ctrl+点击在高亮区域内，拦截点击事件", file=sys.stderr)

                        # 捕获控件信息
                        self.manager.on_mouse_click(x, y)

                        # 返回False表示拦截这个事件，不传递给下方应用程序
                        return False
                else:
                    # 非Ctrl+点击，正常处理
                    self.manager.on_mouse_click(x, y)
        except Exception as e:
            print(f"[ERROR] 处理鼠标点击事件时发生错误: {e}", file=sys.stderr)

        # 返回True表示继续传递这个事件
        return True

    def is_point_in_highlight(self, x, y):
        """检查点是否在高亮区域内"""
        if not self.manager.highlight_renderer.highlight_visible:
            return False

        hr = self.manager.highlight_renderer
        return (hr.highlight_x <= x <= hr.highlight_x + hr.highlight_width and
                hr.highlight_y <= y <= hr.highlight_y + hr.highlight_height)


class WidgetCaptureManager:
    """
    控件捕获管理器
    负责管理整个捕获过程，协调各组件工作
    """
    def __init__(self, debug=False, callback=None):
        self.debug = debug
        self.callback = callback

        # 初始化组件
        self.keyboard_monitor = KeyboardEventMonitor(self, debug)
        self.mouse_monitor = MouseEventMonitor(self, debug)
        self.highlight_renderer = HighlightRenderer(debug)
        self.widget_finder = UNIWidgetFinder(debug)

        # 状态变量
        self.is_capturing = False
        self.ctrl_pressed = False
        self.current_widget_info = None

        # 节流变量
        self.last_move_time = 0
        self.move_throttle = 0.05  # 50ms

        if self.debug:
            print("[DEBUG] 控件捕获管理器初始化完成", file=sys.stderr)

    def start_capture(self):
        """开始捕获控件"""
        if self.is_capturing:
            print("[WARNING] 控件捕获已经在运行", file=sys.stderr)
            return False

        self.is_capturing = True

        # 启动键盘和鼠标监控
        keyboard_ok = self.keyboard_monitor.start()
        mouse_ok = self.mouse_monitor.start()

        if not keyboard_ok or not mouse_ok:
            self.stop_capture()
            return False

        print("控件捕获已启动，请按住Ctrl键并移动鼠标选择控件，单击鼠标左键完成捕获")
        return True

    def stop_capture(self):
        """停止捕获控件"""
        if not self.is_capturing:
            return

        self.is_capturing = False

        # 停止键盘和鼠标监控
        self.keyboard_monitor.stop()
        self.mouse_monitor.stop()

        # 清除高亮
        self.highlight_renderer.clear_highlight()

        # 重置状态
        self.ctrl_pressed = False
        self.current_widget_info = None

        print("控件捕获已停止")

    def on_ctrl_pressed(self):
        """处理Ctrl键按下事件"""
        self.ctrl_pressed = True

    def on_ctrl_released(self):
        """处理Ctrl键释放事件"""
        self.ctrl_pressed = False
        self.highlight_renderer.clear_highlight()
        self.current_widget_info = None

    def on_mouse_move(self, x, y):
        """处理鼠标移动事件"""
        if not self.is_capturing or not self.ctrl_pressed:
            # 如果不再捕获状态或没有按住Ctrl键，清除高亮
            if self.highlight_renderer.highlight_visible:
                self.highlight_renderer.clear_highlight()
                self.current_widget_info = None
            return

        # 节流处理，避免频繁调用
        current_time = time.time()
        if current_time - self.last_move_time < self.move_throttle:
            return
        self.last_move_time = current_time

        # 调用UNI.py获取控件信息
        widget_info, _ = self.widget_finder.find_widget_at(x, y)

        # 只在成功获取到真实控件信息时才绘制高亮边框
        if widget_info and "Coords" in widget_info:
            # 保存当前控件信息
            self.current_widget_info = widget_info

            # 高亮显示控件
            coords = widget_info["Coords"]
            if self.debug:
                print(f"[DEBUG] 高亮控件: x={coords['x']}, y={coords['y']}, width={coords['width']}, height={coords['height']}", file=sys.stderr)

            self.highlight_renderer.highlight_widget(
                coords["x"],
                coords["y"],
                coords["width"],
                coords["height"],
                widget_info
            )
        elif self.highlight_renderer.highlight_visible:
            # 如果没有找到控件但高亮仍然可见，清除高亮
            self.highlight_renderer.clear_highlight()
            self.current_widget_info = None

    def on_mouse_click(self, x, y):
        """处理鼠标点击事件"""
        if not self.is_capturing or not self.ctrl_pressed:
            return

        # 如果已经有当前控件信息，直接使用
        if self.current_widget_info:
            if self.debug:
                print(f"[DEBUG] 使用当前高亮的控件信息进行捕获", file=sys.stderr)
                print(f"[DEBUG] 控件信息: {self.current_widget_info.get('Name', 'Unknown')}", file=sys.stderr)
            self.capture_widget(self.current_widget_info)
            return True
        else:
            # 尝试获取控件信息
            widget_info, _ = self.widget_finder.find_widget_at(x, y)
            if widget_info and "Coords" in widget_info:
                if self.debug:
                    print(f"[DEBUG] 捕获到新的控件信息", file=sys.stderr)
                    print(f"[DEBUG] 控件信息: {widget_info.get('Name', 'Unknown')}", file=sys.stderr)
                self.capture_widget(widget_info)
                return True
            else:
                if self.debug:
                    print(f"[DEBUG] 未找到可捕获的控件", file=sys.stderr)
                return False

    def capture_widget(self, widget_info):
        """捕获控件信息并返回"""
        # 确保输出立即显示
        import functools
        print_flush = functools.partial(print, flush=True)

        print_flush(f"\n\n==== 控件已捕获 ====\n")
        # 优先使用Key字段，如果没有则使用Name字段
        control_name = widget_info.get('Key') or widget_info.get('Name', 'Unknown')
        print_flush(f"控件名称: {control_name}")
        print_flush(f"控件类型: {widget_info.get('Rolename', 'Unknown')}")

        # 打印关键属性
        if 'Coords' in widget_info:
            coords = widget_info['Coords']
            print_flush(f"控件坐标: x={coords.get('x')}, y={coords.get('y')}, width={coords.get('width')}, height={coords.get('height')}")
        if 'ProcessName' in widget_info:
            print_flush(f"进程名: {widget_info.get('ProcessName')}")
        if 'WindowName' in widget_info:
            print_flush(f"窗口名: {widget_info.get('WindowName')}")

        # 输出完整控件信息
        print_flush("\n控件详细信息:")
        print_flush(json.dumps(widget_info, indent=2, ensure_ascii=False, default=str))
        print_flush("\n==== 捕获完成 ====\n")

        # 如果有回调函数，调用它
        if self.callback:
            self.callback(widget_info)

        # 停止捕获
        self.stop_capture()

        # 返回控件信息
        return widget_info

    def cleanup(self):
        """清理资源"""
        self.stop_capture()
        self.highlight_renderer.cleanup()

        if self.debug:
            print("[DEBUG] 控件捕获管理器资源已清理", file=sys.stderr)


def capture_widget(callback=None, debug=False):
    """
    启动控件高亮捕获，并在捕获到控件信息后调用回调函数

    参数:
        callback: 回调函数，在捕获到控件信息后调用，接收一个参数：控件信息字典
        debug: 是否启用调试输出

    返回:
        成功启动捕获则返回True，否则返回False
    """
    # 检查依赖
    if not XLIB_AVAILABLE:
        print("[ERROR] Xlib库未安装，请使用 pip install python-xlib 安装")
        return False

    if not PYNPUT_AVAILABLE:
        print("[ERROR] pynput库未安装，请使用 pip install pynput 安装")
        return False

    if not UNI_AVAILABLE:
        print("[ERROR] UNI模块不可用，请确保路径正确")
        return False

    # 创建控件捕获管理器
    manager = WidgetCaptureManager(debug=debug, callback=callback)

    # 注册信号处理器
    def signal_handler(sig, frame):
        print("\n[INFO] 收到中断信号，停止捕获")
        manager.cleanup()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    # 启动捕获
    return manager.start_capture()


if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="控件高亮捕获工具")
    parser.add_argument("--debug", action="store_true", help="启用调试输出")
    args = parser.parse_args()

    # 定义回调函数
    def on_widget_captured(widget_info):
        print("\n捕获的控件信息:")
        print(json.dumps(widget_info, indent=2, default=str))

    # 启动捕获
    print("启动控件高亮捕获...")
    if not capture_widget(callback=on_widget_captured, debug=args.debug):
        sys.exit(1)

    # 主循环
    try:
        while True:
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n程序已中断")
        sys.exit(0)
