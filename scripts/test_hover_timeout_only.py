#!/usr/bin/env python3
"""
专门测试悬停超时功能
验证鼠标悬停0.5秒后2秒内没有移动会触发重启
"""

import sys
import os
import time
import subprocess
import signal

def test_hover_timeout():
    """测试悬停超时功能"""
    print("=" * 60, file=sys.stderr)
    print("🧪 专门测试悬停超时功能", file=sys.stderr)
    print("=" * 60, file=sys.stderr)
    
    # 启动生命周期管理器，设置较短的超时时间便于测试
    cmd = [
        'python3', 'scripts/start_auto_recording_with_lifecycle.py',
        '--debug',
        '--hover-threshold', '0.3',  # 0.3秒悬停检测
        '--hover-timeout', '1.5',    # 1.5秒超时
        '--slow-threshold', '10.0',  # 设置很高的慢识别阈值，避免干扰
        '--max-slow-count', '10',    # 设置很高的慢识别次数，避免干扰
        '--duration', '20'           # 20秒后自动停止
    ]
    
    env = os.environ.copy()
    env['DISPLAY'] = ':0'
    
    try:
        print("[TEST] 🚀 启动生命周期管理器（悬停超时测试模式）...", file=sys.stderr)
        print(f"[TEST] 悬停检测: 0.3秒, 超时阈值: 1.5秒", file=sys.stderr)
        print(f"[TEST] 📝 请在启动后保持鼠标静止1.8秒以上来触发悬停超时", file=sys.stderr)
        
        process = subprocess.Popen(
            cmd,
            cwd='/home/<USER>/kylin-robot-ide',
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"[TEST] ✅ 进程已启动，PID: {process.pid}", file=sys.stderr)
        
        # 监控进程输出
        start_time = time.time()
        hover_detected = False
        hover_timeout_detected = False
        restart_triggered = False
        
        while process.poll() is None:
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 读取stderr输出
            try:
                import select
                if select.select([process.stderr], [], [], 0.1)[0]:
                    line = process.stderr.readline()
                    if line:
                        line_clean = line.rstrip()
                        print(f"[OUTPUT] {line_clean}", file=sys.stderr)
                        
                        # 检测悬停事件
                        if "🎯 检测到悬停" in line:
                            hover_detected = True
                            print(f"[DETECT] ✅ 悬停检测成功", file=sys.stderr)
                            
                        # 检测悬停超时
                        if "🚨 悬停超时检测" in line:
                            hover_timeout_detected = True
                            print(f"[DETECT] ✅ 悬停超时检测成功", file=sys.stderr)
                            
                        # 检测重启触发
                        if "🔄 触发管理器重启" in line:
                            restart_triggered = True
                            print(f"[DETECT] ✅ 重启触发成功", file=sys.stderr)
                            
            except Exception as e:
                if "Resource temporarily unavailable" not in str(e):
                    print(f"[DEBUG] 读取输出时出错: {e}", file=sys.stderr)
            
            # 定期输出状态
            if int(elapsed) % 5 == 0 and elapsed > 0:
                print(f"[STATUS] 运行时间: {elapsed:.0f}秒", file=sys.stderr)
                print(f"   悬停检测: {'✅' if hover_detected else '❌'}", file=sys.stderr)
                print(f"   悬停超时: {'✅' if hover_timeout_detected else '❌'}", file=sys.stderr)
                print(f"   重启触发: {'✅' if restart_triggered else '❌'}", file=sys.stderr)
            
            # 超时保护
            if elapsed > 30:  # 30秒超时
                print("[TEST] ⏰ 测试超时，强制结束", file=sys.stderr)
                break
                
            time.sleep(0.1)
        
        # 等待进程结束
        try:
            stdout, stderr = process.communicate(timeout=5)
            return_code = process.returncode
            
            print(f"[TEST] 📊 测试结果:", file=sys.stderr)
            print(f"   进程返回码: {return_code}", file=sys.stderr)
            print(f"   运行时间: {time.time() - start_time:.1f}秒", file=sys.stderr)
            print(f"   悬停检测: {'✅ 成功' if hover_detected else '❌ 失败'}", file=sys.stderr)
            print(f"   悬停超时: {'✅ 成功' if hover_timeout_detected else '❌ 失败'}", file=sys.stderr)
            print(f"   重启触发: {'✅ 成功' if restart_triggered else '❌ 失败'}", file=sys.stderr)
            
            # 测试评估
            if hover_detected and hover_timeout_detected and restart_triggered:
                print(f"[RESULT] 🎉 悬停超时功能测试完全成功！", file=sys.stderr)
            elif hover_detected and hover_timeout_detected:
                print(f"[RESULT] ⚠️ 悬停超时检测成功，但重启触发可能有问题", file=sys.stderr)
            elif hover_detected:
                print(f"[RESULT] ⚠️ 悬停检测成功，但超时功能可能有问题", file=sys.stderr)
            else:
                print(f"[RESULT] ❌ 悬停检测功能可能有问题", file=sys.stderr)
                        
        except subprocess.TimeoutExpired:
            print("[TEST] ⚠️ 进程未正常结束，强制终止", file=sys.stderr)
            process.kill()
            process.wait()
            
    except KeyboardInterrupt:
        print(f"\n[TEST] 🛑 用户中断测试", file=sys.stderr)
        if process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                
    except Exception as e:
        print(f"[ERROR] 测试过程中发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        
    finally:
        print("[TEST] 👋 测试结束", file=sys.stderr)


def main():
    """主函数"""
    try:
        test_hover_timeout()
    except Exception as e:
        print(f"[ERROR] 主函数发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)


if __name__ == "__main__":
    main()
