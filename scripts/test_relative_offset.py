#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试相对偏移百分比计算功能
验证鼠标点击位置相对于控件中心的偏移计算是否正确
"""

import sys
from pathlib import Path

# 添加scripts目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_relative_offset_calculation():
    """测试相对偏移计算功能"""
    try:
        from auto_recording_manager import AutoRecordingManager

        print("=" * 80)
        print("🧮 相对偏移百分比计算功能测试")
        print("=" * 80)

        # 创建AutoRecordingManager实例
        manager = AutoRecordingManager(debug=True)

        # 测试用例1: 控件中心点击
        print("\n📍 测试1: 控件中心点击")
        print("-" * 50)
        coords = {
            'left': 100,
            'top': 50,
            'width': 200,
            'height': 100
        }
        # 左上角点: (100, 50)
        topleft_x = 100  # 控件左上角X坐标
        topleft_y = 50   # 控件左上角Y坐标

        result = manager._calculate_relative_offset_percentage(topleft_x, topleft_y, coords)
        print(f"控件区域: ({coords['left']}, {coords['top']}, {coords['width']}, {coords['height']})")
        print(f"点击位置: ({topleft_x}, {topleft_y})")
        print(f"期望结果: 偏移(0%, 0%), 距左上角0%")
        print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距左上角{result['distance_from_topleft_percent']:.1f}%")

        # 验证结果
        if abs(result['offset_x_percent']) < 0.1 and abs(result['offset_y_percent']) < 0.1:
            print("✅ 左上角点击测试通过")
        else:
            print("❌ 左上角点击测试失败")

        # 测试用例2: 左上角点击
        print("\n📍 测试2: 左上角点击")
        print("-" * 50)
        corner_x = 100  # 左边界
        corner_y = 50   # 上边界

        result = manager._calculate_relative_offset_percentage(corner_x, corner_y, coords)
        print(f"点击位置: ({corner_x}, {corner_y})")
        print(f"期望结果: 偏移(-50%, -25%), 距中心约55.9%")
        print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距中心{result['distance_from_center_percent']:.1f}%")

        if abs(result['offset_x_percent'] + 50) < 1 and abs(result['offset_y_percent'] + 25) < 1:
            print("✅ 左上角点击测试通过")
        else:
            print("❌ 左上角点击测试失败")

        # 测试用例3: 右下角点击
        print("\n📍 测试3: 右下角点击")
        print("-" * 50)
        corner_x = 300  # 右边界 (100 + 200)
        corner_y = 150  # 下边界 (50 + 100)

        result = manager._calculate_relative_offset_percentage(corner_x, corner_y, coords)
        print(f"点击位置: ({corner_x}, {corner_y})")
        print(f"期望结果: 偏移(50%, 25%), 距中心约55.9%")
        print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距中心{result['distance_from_center_percent']:.1f}%")

        if abs(result['offset_x_percent'] - 50) < 1 and abs(result['offset_y_percent'] - 25) < 1:
            print("✅ 右下角点击测试通过")
        else:
            print("❌ 右下角点击测试失败")

        # 测试用例4: 异常边界情况
        print("\n📍 测试4: 控件外点击")
        print("-" * 50)
        outside_x = 350  # 超出右边界
        outside_y = 200  # 超出下边界

        result = manager._calculate_relative_offset_percentage(outside_x, outside_y, coords)
        print(f"点击位置: ({outside_x}, {outside_y})")
        print(f"实际结果: 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距中心{result['distance_from_center_percent']:.1f}%")
        print("🔍 控件外点击也能正常计算偏移百分比")

        # 测试用例5: 实际场景模拟
        print("\n📍 测试5: 实际控件场景模拟")
        print("-" * 50)

        # 模拟一个实际的按钮控件
        button_coords = {
            'left': 552,
            'top': 224,
            'width': 80,
            'height': 30
        }

        # 模拟点击按钮的不同位置
        test_points = [
            (592, 239, "中心点击"),       # 中心: (552+40, 224+15)
            (562, 234, "左上偏移"),       # 左上偏移
            (582, 244, "右下偏移"),       # 右下偏移
        ]

        for click_x, click_y, desc in test_points:
            result = manager._calculate_relative_offset_percentage(click_x, click_y, button_coords)
            print(f"{desc}: 点击({click_x},{click_y}) -> 偏移({result['offset_x_percent']:.1f}%, {result['offset_y_percent']:.1f}%), 距中心{result['distance_from_center_percent']:.1f}%")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_edge_cases():
    """测试边界情况"""
    try:
        from auto_recording_manager import AutoRecordingManager

        print("\n" + "=" * 80)
        print("🔍 边界情况测试")
        print("=" * 80)

        manager = AutoRecordingManager(debug=True)

        # 测试1: 零宽度或零高度控件
        print("\n📍 测试1: 零尺寸控件")
        print("-" * 50)
        try:
            coords = {'left': 100, 'top': 100, 'width': 0, 'height': 100}
            result = manager._calculate_relative_offset_percentage(100, 100, coords)
            print("❌ 零宽度控件应该抛出异常")
        except Exception as e:
            print(f"✅ 零宽度控件正确抛出异常: {e}")

        # 测试2: 缺少字段的coords
        print("\n📍 测试2: 缺少字段的coords")
        print("-" * 50)
        try:
            coords = {'left': 100, 'top': 100}  # 缺少width和height
            result = manager._calculate_relative_offset_percentage(100, 100, coords)
            print("❌ 缺少字段应该抛出异常")
        except Exception as e:
            print(f"✅ 缺少字段正确抛出异常: {e}")

        # 测试3: 非数字类型的coords
        print("\n📍 测试3: 非数字类型coords")
        print("-" * 50)
        try:
            coords = {'left': 'abc', 'top': 100, 'width': 100, 'height': 100}
            result = manager._calculate_relative_offset_percentage(100, 100, coords)
            print("❌ 非数字类型应该抛出异常")
        except Exception as e:
            print(f"✅ 非数字类型正确抛出异常: {e}")

    except Exception as e:
        print(f"❌ 边界测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 相对偏移百分比计算功能完整测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))

    # 基础功能测试
    test_relative_offset_calculation()

    # 边界情况测试
    test_edge_cases()

    print("\n" + "=" * 80)
    print("✅ 测试完成!")
    print("=" * 80)
    print("\n📋 功能说明:")
    print("✨ offset_x_percent: 水平偏移百分比，负值表示左偏，正值表示右偏")
    print("✨ offset_y_percent: 垂直偏移百分比，负值表示上偏，正值表示下偏")
    print("✨ distance_from_center_percent: 距离中心点的距离百分比")
    print("✨ 计算基准: 以控件中心为原点，控件尺寸为100%标准")

if __name__ == "__main__":
    import time
    main()
