#!/usr/bin/env python3
"""
带生命周期管理的AutoRecording启动脚本
当控件识别过慢时自动重启管理器
增加鼠标悬停事件监控，当悬停后2秒内没有收到控件识别结果时重启管理器
新增控件识别超时监控器，当控件识别超过3秒没有完成时杀死并重启进程
"""

import sys
import os
import argparse
import signal
import time
import threading
import queue
from typing import Optional

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from auto_recording_lifecycle_manager import AutoRecordingLifecycleManager
except ImportError as e:
    print(f"[ERROR] 无法导入生命周期管理器: {e}", file=sys.stderr)
    print(f"[ERROR] 请确保scripts/auto_recording_lifecycle_manager.py文件存在", file=sys.stderr)
    sys.exit(1)

# 导入新的控件识别监控器
try:
    from widget_recognition_monitor import WidgetRecognitionMonitor
    WIDGET_MONITOR_AVAILABLE = True
except ImportError as e:
    print(f"[WARNING] 无法导入控件识别监控器: {e}", file=sys.stderr)
    WIDGET_MONITOR_AVAILABLE = False

# 尝试导入pynput用于鼠标监听
try:
    from pynput import mouse
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print(f"[WARNING] pynput不可用，鼠标悬停监控将被禁用", file=sys.stderr)


class HoverTimeoutMonitor:
    """
    鼠标悬停超时监控器
    监控鼠标悬停事件，当悬停0.5秒后2秒内没有收到控件识别结果时触发重启
    """

    def __init__(self, lifecycle_manager, hover_threshold: float = 0.5, timeout_threshold: float = 2.0, debug: bool = False):
        self.lifecycle_manager = lifecycle_manager
        self.hover_threshold = hover_threshold  # 悬停检测阈值（秒）
        self.timeout_threshold = timeout_threshold  # 控件识别超时阈值（秒）
        self.debug = debug

        # 状态管理
        self.running = False
        self.mouse_listener = None
        self.current_position = (0, 0)
        self.last_move_time = 0
        self.hover_timer = None
        self.timeout_timer = None
        self.lock = threading.Lock()

        # 监控统计
        self.hover_count = 0
        self.timeout_count = 0
        self.restart_triggered_count = 0

    def start(self):
        """启动悬停监控"""
        if not PYNPUT_AVAILABLE:
            print(f"[HOVER] ⚠️ pynput不可用，无法启动鼠标悬停监控", file=sys.stderr)
            return False

        with self.lock:
            if self.running:
                return True

            self.running = True

        try:
            # 启动鼠标监听器
            self.mouse_listener = mouse.Listener(on_move=self._on_mouse_move)
            self.mouse_listener.start()

            print(f"[HOVER] 🎯 鼠标悬停监控已启动", file=sys.stderr)
            print(f"[HOVER] 📊 悬停阈值: {self.hover_threshold}秒, 超时阈值: {self.timeout_threshold}秒", file=sys.stderr)
            return True

        except Exception as e:
            print(f"[ERROR] 启动鼠标悬停监控失败: {e}", file=sys.stderr)
            self.running = False
            return False

    def stop(self):
        """停止悬停监控"""
        with self.lock:
            if not self.running:
                return

            self.running = False

            # 取消定时器
            if self.hover_timer:
                self.hover_timer.cancel()
                self.hover_timer = None

            if self.timeout_timer:
                self.timeout_timer.cancel()
                self.timeout_timer = None

        # 停止鼠标监听器
        if self.mouse_listener:
            try:
                self.mouse_listener.stop()
                self.mouse_listener = None
            except Exception as e:
                if self.debug:
                    print(f"[DEBUG] 停止鼠标监听器时发生错误: {e}", file=sys.stderr)

        print(f"[HOVER] 🛑 鼠标悬停监控已停止", file=sys.stderr)

    def _on_mouse_move(self, x: int, y: int):
        """处理鼠标移动事件"""
        if not self.running:
            return

        current_time = time.time()

        with self.lock:
            # 计算移动距离
            distance = ((x - self.current_position[0])**2 + (y - self.current_position[1])**2)**0.5

            # 如果移动距离超过阈值，重置悬停检测
            if distance > 5:  # 5像素移动阈值
                if self.debug:
                    print(f"[DEBUG] 鼠标移动: ({x}, {y}), 距离={distance:.1f}px", file=sys.stderr)

                # 取消之前的定时器
                if self.hover_timer:
                    self.hover_timer.cancel()
                    self.hover_timer = None

                if self.timeout_timer:
                    self.timeout_timer.cancel()
                    self.timeout_timer = None

                # 更新位置和时间
                self.current_position = (x, y)
                self.last_move_time = current_time

                # 启动新的悬停检测定时器
                self.hover_timer = threading.Timer(
                    self.hover_threshold,
                    self._on_hover_detected,
                    args=(x, y, current_time)
                )
                self.hover_timer.start()

    def _on_hover_detected(self, x: int, y: int, start_time: float):
        """悬停检测到，启动超时监控"""
        if not self.running:
            return

        with self.lock:
            self.hover_count += 1

        if self.debug:
            print(f"[DEBUG] 🎯 检测到悬停: ({x}, {y})", file=sys.stderr)

        # 启动超时监控定时器
        self.timeout_timer = threading.Timer(
            self.timeout_threshold,
            self._on_timeout_detected,
            args=(x, y, start_time)
        )
        self.timeout_timer.start()

    def _on_timeout_detected(self, x: int, y: int, start_time: float):
        """检测到超时，触发重启"""
        if not self.running:
            return

        with self.lock:
            self.timeout_count += 1
            self.restart_triggered_count += 1

        elapsed_time = time.time() - start_time
        print(f"[HOVER] 🚨 悬停超时检测: 位置=({x}, {y}), 耗时={elapsed_time:.1f}秒", file=sys.stderr)
        print(f"[HOVER] 🔄 触发管理器重启 (第{self.restart_triggered_count}次)", file=sys.stderr)

        # 触发生命周期管理器重启
        if self.lifecycle_manager:
            try:
                self.lifecycle_manager._schedule_restart()
            except Exception as e:
                print(f"[ERROR] 触发重启时发生错误: {e}", file=sys.stderr)

    def get_statistics(self):
        """获取监控统计信息"""
        with self.lock:
            return {
                'running': self.running,
                'hover_count': self.hover_count,
                'timeout_count': self.timeout_count,
                'restart_triggered_count': self.restart_triggered_count,
                'hover_threshold': self.hover_threshold,
                'timeout_threshold': self.timeout_threshold
            }


# 全局变量
lifecycle_manager = None
hover_monitor = None

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n[MAIN] 🛑 收到信号 {signum}，正在优雅退出...", file=sys.stderr)
    global lifecycle_manager, hover_monitor

    # 停止悬停监控
    if hover_monitor:
        hover_monitor.stop()

    # 停止生命周期管理器
    if lifecycle_manager:
        lifecycle_manager.stop()

    sys.exit(0)

def main():
    """主函数"""
    global lifecycle_manager, hover_monitor

    # 设置DISPLAY环境变量
    if 'DISPLAY' not in os.environ or not os.environ['DISPLAY']:
        os.environ['DISPLAY'] = ':0'
        print(f"[MAIN] 设置DISPLAY环境变量为: {os.environ['DISPLAY']}", file=sys.stderr)

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='带生命周期管理的AutoRecording启动器')
    parser.add_argument('--slow-threshold', type=float, default=3.0,
                       help='慢识别阈值（秒），默认3.0')
    parser.add_argument('--max-slow-count', type=int, default=1,
                       help='最大慢识别次数，默认1')
    parser.add_argument('--restart-delay', type=float, default=2.0,
                       help='重启延迟时间（秒），默认2.0')
    parser.add_argument('--max-restarts', type=int, default=5,
                       help='最大重启次数，默认5')
    parser.add_argument('--restart-cooldown', type=float, default=30.0,
                       help='重启冷却时间（秒），默认30.0')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--json-output', action='store_true',
                       help='启用JSON输出模式')
    parser.add_argument('--storage-path', type=str, default='recordings',
                       help='录制文件存储路径，默认recordings')

    # 悬停监控参数
    parser.add_argument('--hover-threshold', type=float, default=0.5,
                       help='鼠标悬停检测阈值（秒），默认0.5')
    parser.add_argument('--hover-timeout', type=float, default=2.0,
                       help='悬停后控件识别超时阈值（秒），默认2.0')
    parser.add_argument('--disable-hover-monitor', action='store_true',
                       help='禁用鼠标悬停监控')

    # 🆕 控件识别监控参数
    parser.add_argument('--enable-widget-monitor', action='store_true',
                       help='启用控件识别超时监控器（推荐）')
    parser.add_argument('--widget-timeout', type=float, default=3.0,
                       help='控件识别超时阈值（秒），默认3.0')

    # 兼容auto_recording_manager.py的参数
    parser.add_argument('--duration', type=int, help='录制时长（秒）')
    parser.add_argument('--test-case-id', type=str, help='测试用例ID')
    parser.add_argument('--testcase-path', type=str, help='测试用例路径')
    parser.add_argument('--app-name', type=str, help='录制的应用名称')

    args = parser.parse_args()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        print("=" * 80, file=sys.stderr)
        print("🚀 AutoRecording生命周期管理启动器", file=sys.stderr)
        print("=" * 80, file=sys.stderr)
        print(f"📊 配置参数:", file=sys.stderr)
        print(f"   慢识别阈值: {args.slow_threshold}秒", file=sys.stderr)
        print(f"   最大慢识别次数: {args.max_slow_count}", file=sys.stderr)
        print(f"   重启延迟: {args.restart_delay}秒", file=sys.stderr)
        print(f"   最大重启次数: {args.max_restarts}", file=sys.stderr)
        print(f"   重启冷却时间: {args.restart_cooldown}秒", file=sys.stderr)
        print(f"   调试模式: {args.debug}", file=sys.stderr)
        print(f"   JSON输出模式: {args.json_output}", file=sys.stderr)
        print(f"   存储路径: {args.storage_path}", file=sys.stderr)
        print(f"📊 悬停监控参数:", file=sys.stderr)
        print(f"   悬停检测阈值: {args.hover_threshold}秒", file=sys.stderr)
        print(f"   控件识别超时: {args.hover_timeout}秒", file=sys.stderr)
        print(f"   悬停监控启用: {not args.disable_hover_monitor and PYNPUT_AVAILABLE}", file=sys.stderr)
        print(f"📊 控件识别监控参数:", file=sys.stderr)
        print(f"   控件识别监控启用: {args.enable_widget_monitor and WIDGET_MONITOR_AVAILABLE}", file=sys.stderr)
        print(f"   控件识别超时阈值: {args.widget_timeout}秒", file=sys.stderr)
        print("=" * 80, file=sys.stderr)

        # 🆕 检查是否使用新的控件识别监控器
        if args.enable_widget_monitor and WIDGET_MONITOR_AVAILABLE:
            print(f"[MAIN] 🎯 使用控件识别超时监控器模式", file=sys.stderr)

            # 构建auto_recording_manager.py的启动参数
            target_script = os.path.join(os.path.dirname(__file__), 'auto_recording_manager.py')
            target_args = []

            if args.debug:
                target_args.append('--debug')
            if args.json_output:
                target_args.append('--json-output')
            if args.storage_path:
                target_args.extend(['--storage-path', args.storage_path])
            if args.duration:
                target_args.extend(['--duration', str(args.duration)])
            if args.test_case_id:
                target_args.extend(['--test-case-id', args.test_case_id])
            if args.testcase_path:
                target_args.extend(['--testcase-path', args.testcase_path])
            if args.app_name:
                target_args.extend(['--app-name', args.app_name])

            # 创建控件识别监控器
            widget_monitor = WidgetRecognitionMonitor(
                timeout_threshold=args.widget_timeout,
                debug=args.debug
            )

            # 启动监控器
            target_process_args = ['python3', target_script] + target_args
            if widget_monitor.start(target_process_args):
                print(f"[MAIN] 🎯 控件识别监控器已启动", file=sys.stderr)
                print(f"[MAIN] 📊 监控目标: {target_script}", file=sys.stderr)
                print(f"[MAIN] 📊 超时阈值: {args.widget_timeout}秒", file=sys.stderr)

                # 主循环
                try:
                    last_status_time = 0
                    status_interval = 30

                    while widget_monitor.running:
                        current_time = time.time()

                        # 定期输出状态
                        if current_time - last_status_time >= status_interval:
                            stats = widget_monitor.get_statistics()
                            print(f"[MAIN] 📈 监控器状态报告:", file=sys.stderr)
                            print(f"   总识别次数: {stats['total_recognitions']}", file=sys.stderr)
                            print(f"   超时次数: {stats['timeout_count']}", file=sys.stderr)
                            print(f"   重启次数: {stats['restart_count']}", file=sys.stderr)
                            print(f"   活跃识别任务: {stats['active_recognitions']}", file=sys.stderr)
                            print(f"   目标进程PID: {stats['target_process_pid']}", file=sys.stderr)
                            print(f"[MAIN] " + "─" * 60, file=sys.stderr)
                            last_status_time = current_time

                        time.sleep(1)

                except KeyboardInterrupt:
                    print(f"\n[MAIN] 🛑 用户中断，正在退出...", file=sys.stderr)
                finally:
                    widget_monitor.stop()
                    print(f"[MAIN] 👋 程序已退出", file=sys.stderr)

                return
            else:
                print(f"[ERROR] 控件识别监控器启动失败，回退到传统模式", file=sys.stderr)

        # 传统模式：创建生命周期管理器
        print(f"[MAIN] 🎯 使用传统生命周期管理器模式", file=sys.stderr)
        lifecycle_manager = AutoRecordingLifecycleManager(
            slow_threshold=args.slow_threshold,
            max_slow_count=args.max_slow_count,
            restart_delay=args.restart_delay,
            max_restarts=args.max_restarts,
            restart_cooldown=args.restart_cooldown
        )

        # 配置管理器参数 - 只传递AutoRecordingManager构造函数支持的参数
        manager_config = {
            'debug': args.debug,
            'json_output': args.json_output,
            'storage_path': args.storage_path
        }

        # 将扩展参数保存，在管理器启动时使用
        extended_config = {}
        if args.duration:
            extended_config['duration'] = args.duration
        if args.test_case_id:
            extended_config['test_case_id'] = args.test_case_id
        if args.testcase_path:
            extended_config['testcase_path'] = args.testcase_path
        if args.app_name:
            extended_config['app_name'] = args.app_name

        lifecycle_manager.configure_manager(**manager_config)
        lifecycle_manager.set_extended_config(extended_config)

        # 初始化悬停监控器
        if not args.disable_hover_monitor and PYNPUT_AVAILABLE:
            hover_monitor = HoverTimeoutMonitor(
                lifecycle_manager=lifecycle_manager,
                hover_threshold=args.hover_threshold,
                timeout_threshold=args.hover_timeout,
                debug=args.debug
            )

            # 启动悬停监控
            if hover_monitor.start():
                print(f"[MAIN] 🎯 鼠标悬停监控已启动", file=sys.stderr)
            else:
                print(f"[MAIN] ⚠️ 鼠标悬停监控启动失败", file=sys.stderr)
                hover_monitor = None
        else:
            if args.disable_hover_monitor:
                print(f"[MAIN] ⚠️ 鼠标悬停监控已被禁用", file=sys.stderr)
            else:
                print(f"[MAIN] ⚠️ pynput不可用，鼠标悬停监控已禁用", file=sys.stderr)

        # 启动生命周期管理器
        lifecycle_manager.start()

        print(f"[MAIN] 🎯 AutoRecording已启动，生命周期管理器正在监控性能", file=sys.stderr)
        print(f"[MAIN] 📊 按Ctrl+C退出程序", file=sys.stderr)
        restart_text = "第一次" if args.max_slow_count == 1 else f"连续{args.max_slow_count}次"
        print(f"[MAIN] 🔄 当{restart_text}识别超过{args.slow_threshold}秒时将自动重启", file=sys.stderr)
        if hover_monitor:
            print(f"[MAIN] 🖱️ 当鼠标悬停{args.hover_threshold}秒后{args.hover_timeout}秒内无控件识别结果时也将重启", file=sys.stderr)

        # 主循环 - 定期报告状态
        status_interval = 30  # 每30秒报告一次状态
        last_status_time = 0

        while lifecycle_manager.is_running():
            current_time = time.time()

            # 定期输出状态
            if current_time - last_status_time >= status_interval:
                status = lifecycle_manager.get_status()
                stats = status['performance_stats']

                print(f"[MAIN] 📈 状态报告:", file=sys.stderr)
                print(f"   运行状态: {'正常' if status['running'] else '已停止'}", file=sys.stderr)
                print(f"   管理器活跃: {'是' if status['manager_active'] else '否'}", file=sys.stderr)
                print(f"   重启次数: {status['restart_count']}/{status['config']['max_restarts']}", file=sys.stderr)
                print(f"   总识别次数: {stats['total_recognitions']}", file=sys.stderr)
                print(f"   慢识别次数: {stats['slow_recognitions']}", file=sys.stderr)
                print(f"   当前连续慢识别: {stats['current_slow_count']}/{status['config']['max_slow_count']}", file=sys.stderr)

                # 添加悬停监控状态
                if hover_monitor:
                    hover_stats = hover_monitor.get_statistics()
                    print(f"   悬停监控状态: {'运行中' if hover_stats['running'] else '已停止'}", file=sys.stderr)
                    print(f"   悬停检测次数: {hover_stats['hover_count']}", file=sys.stderr)
                    print(f"   悬停超时次数: {hover_stats['timeout_count']}", file=sys.stderr)
                    print(f"   悬停触发重启次数: {hover_stats['restart_triggered_count']}", file=sys.stderr)

                if status['restart_count'] > 0:
                    last_restart = time.ctime(status['last_restart_time']) if status['last_restart_time'] > 0 else '无'
                    print(f"   最后重启时间: {last_restart}", file=sys.stderr)

                print(f"[MAIN] " + "─" * 60, file=sys.stderr)
                last_status_time = current_time

            time.sleep(1)

    except KeyboardInterrupt:
        print(f"\n[MAIN] 🛑 用户中断，正在退出...", file=sys.stderr)
    except Exception as e:
        print(f"[ERROR] 主程序发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
    finally:
        # 停止悬停监控
        if hover_monitor:
            hover_monitor.stop()

        # 停止生命周期管理器
        if lifecycle_manager:
            lifecycle_manager.stop()

        print(f"[MAIN] 👋 程序已退出", file=sys.stderr)

if __name__ == "__main__":
    main()
