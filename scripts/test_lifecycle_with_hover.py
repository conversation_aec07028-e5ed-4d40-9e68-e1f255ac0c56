#!/usr/bin/env python3
"""
测试带悬停监控的生命周期管理功能
验证清理后的auto_recording_manager.py不会自动重启
"""

import sys
import os
import time
import subprocess
import signal

# 添加scripts目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_lifecycle_manager():
    """测试生命周期管理器"""
    print("=" * 60, file=sys.stderr)
    print("🧪 测试带悬停监控的生命周期管理器", file=sys.stderr)
    print("=" * 60, file=sys.stderr)

    # 启动生命周期管理器
    cmd = [
        'python3', 'scripts/start_auto_recording_with_lifecycle.py',
        '--debug',
        '--hover-threshold', '0.5',
        '--hover-timeout', '2.0',
        '--slow-threshold', '3.0',
        '--max-slow-count', '1',
        '--duration', '30'  # 30秒后自动停止
    ]

    env = os.environ.copy()
    env['DISPLAY'] = ':0'

    try:
        print("[TEST] 🚀 启动生命周期管理器...", file=sys.stderr)
        print(f"[TEST] 命令: {' '.join(cmd)}", file=sys.stderr)

        process = subprocess.Popen(
            cmd,
            cwd='/home/<USER>/kylin-robot-ide',
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        print(f"[TEST] ✅ 进程已启动，PID: {process.pid}", file=sys.stderr)
        print("[TEST] 📝 请移动鼠标测试悬停监控功能", file=sys.stderr)
        print("[TEST] 📝 观察是否有自动重启行为", file=sys.stderr)
        print("[TEST] 📝 30秒后程序将自动停止", file=sys.stderr)

        # 监控进程输出
        start_time = time.time()
        restart_count = 0
        hover_timeout_count = 0

        while process.poll() is None:
            current_time = time.time()
            elapsed = current_time - start_time

            # 读取stderr输出
            try:
                # 非阻塞读取
                import select
                if select.select([process.stderr], [], [], 0.1)[0]:
                    line = process.stderr.readline()
                    if line:
                        print(f"[OUTPUT] {line.rstrip()}", file=sys.stderr)

                        # 统计重启次数 - 只统计真正的重启事件
                        if ("开始第" in line and "次重启" in line) or ("触发管理器重启" in line):
                            restart_count += 1
                            print(f"[STATS] 🔄 检测到重启事件 (总计: {restart_count}次)", file=sys.stderr)

                        # 统计悬停超时次数
                        if "悬停超时检测" in line:
                            hover_timeout_count += 1
                            print(f"[STATS] 🖱️ 检测到悬停超时 (总计: {hover_timeout_count}次)", file=sys.stderr)

            except Exception as e:
                if "Resource temporarily unavailable" not in str(e):
                    print(f"[DEBUG] 读取输出时出错: {e}", file=sys.stderr)

            # 定期输出状态
            if int(elapsed) % 10 == 0 and elapsed > 0:
                print(f"[STATS] 📊 运行时间: {elapsed:.0f}秒, 重启次数: {restart_count}, 悬停超时: {hover_timeout_count}", file=sys.stderr)

            # 超时保护
            if elapsed > 60:  # 60秒超时
                print("[TEST] ⏰ 测试超时，强制结束", file=sys.stderr)
                break

            time.sleep(0.1)

        # 等待进程结束
        try:
            stdout, stderr = process.communicate(timeout=5)
            return_code = process.returncode

            print(f"[TEST] 📊 最终统计:", file=sys.stderr)
            print(f"   进程返回码: {return_code}", file=sys.stderr)
            print(f"   运行时间: {time.time() - start_time:.1f}秒", file=sys.stderr)
            print(f"   重启次数: {restart_count}", file=sys.stderr)
            print(f"   悬停超时次数: {hover_timeout_count}", file=sys.stderr)

            if stderr:
                print(f"[TEST] 📝 最后的stderr输出:", file=sys.stderr)
                for line in stderr.split('\n')[-10:]:  # 显示最后10行
                    if line.strip():
                        print(f"   {line}", file=sys.stderr)

        except subprocess.TimeoutExpired:
            print("[TEST] ⚠️ 进程未正常结束，强制终止", file=sys.stderr)
            process.kill()
            process.wait()

    except KeyboardInterrupt:
        print(f"\n[TEST] 🛑 用户中断测试", file=sys.stderr)
        if process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()

    except Exception as e:
        print(f"[ERROR] 测试过程中发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

    finally:
        print("[TEST] 👋 测试结束", file=sys.stderr)


def main():
    """主函数"""
    try:
        test_lifecycle_manager()
    except Exception as e:
        print(f"[ERROR] 主函数发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)


if __name__ == "__main__":
    main()
