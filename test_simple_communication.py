#!/usr/bin/env python3
"""
简单的通信测试
验证UDP通信和超时检测机制
"""

import sys
import os
import time
import json
import socket
import threading
import subprocess
from pathlib import Path

# 添加scripts目录到路径
scripts_dir = Path(__file__).parent / 'scripts'
sys.path.insert(0, str(scripts_dir))

def test_udp_communication():
    """测试UDP通信"""
    print("🧪 测试UDP通信机制")
    print("=" * 50)

    try:
        from widget_recognition_monitor import WidgetRecognitionMonitor

        # 创建监控器
        monitor = WidgetRecognitionMonitor(timeout_threshold=3.0, debug=True)

        # 手动初始化socket（不启动目标进程）
        monitor.listen_port = 12346  # 使用不同的端口避免冲突
        monitor.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        monitor.socket.bind(('127.0.0.1', 12346))
        monitor.socket.settimeout(1.0)
        monitor.running = True

        # 启动监听线程
        monitor.listen_thread = threading.Thread(target=monitor._listen_loop, daemon=True)
        monitor.listen_thread.start()

        print(f"✅ 监控器已启动，监听端口: 12346")

        # 创建测试客户端
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

        # 测试1: 正常的开始-结束流程
        print(f"\n📍 测试1: 正常的开始-结束流程")

        session_id = 'test_session_001'

        # 发送开始信号
        start_message = {
            'session_id': session_id,
            'signal_type': 'recognition_start',
            'timestamp': time.time(),
            'data': {'x': 100, 'y': 200, 'recognition_type': 'test'}
        }

        print(f"📤 发送识别开始信号...")
        client_socket.sendto(
            json.dumps(start_message).encode('utf-8'),
            ('127.0.0.1', 12346)
        )

        # 等待2秒（小于3秒超时）
        time.sleep(2)

        # 发送结束信号
        end_message = {
            'session_id': session_id,
            'signal_type': 'recognition_end',
            'timestamp': time.time(),
            'data': {'x': 100, 'y': 200, 'duration': 1.8, 'success': True, 'recognition_type': 'test'}
        }

        print(f"📤 发送识别结束信号...")
        client_socket.sendto(
            json.dumps(end_message).encode('utf-8'),
            ('127.0.0.1', 12346)
        )

        time.sleep(1)

        # 检查统计信息
        stats = monitor.get_statistics()
        print(f"📊 测试1结果: 总识别={stats['total_recognitions']}, 超时={stats['timeout_count']}")

        # 测试2: 超时流程
        print(f"\n📍 测试2: 超时流程（不发送结束信号）")

        session_id = 'test_session_002'

        # 发送开始信号
        start_message = {
            'session_id': session_id,
            'signal_type': 'recognition_start',
            'timestamp': time.time(),
            'data': {'x': 300, 'y': 400, 'recognition_type': 'timeout_test'}
        }

        print(f"📤 发送识别开始信号...")
        client_socket.sendto(
            json.dumps(start_message).encode('utf-8'),
            ('127.0.0.1', 12346)
        )

        # 等待4秒（超过3秒超时）
        print(f"⏰ 等待4秒，应该在3秒后触发超时...")
        time.sleep(4)

        # 检查统计信息
        stats = monitor.get_statistics()
        print(f"📊 测试2结果: 总识别={stats['total_recognitions']}, 超时={stats['timeout_count']}")

        # 清理
        client_socket.close()
        monitor.stop()

        print(f"\n✅ 通信测试完成")

        if stats['total_recognitions'] >= 2 and stats['timeout_count'] >= 1:
            print(f"🎉 通信和超时检测功能正常！")
            return True
        else:
            print(f"❌ 通信或超时检测有问题")
            return False

    except Exception as e:
        print(f"❌ 通信测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monitor_with_mock_process():
    """测试监控器与模拟进程"""
    print("\n🧪 测试监控器与模拟进程")
    print("=" * 50)

    try:
        # 创建一个简单的模拟脚本
        mock_script = scripts_dir / 'mock_auto_recording.py'

        mock_content = '''#!/usr/bin/env python3
import sys
import time
import json
import socket

# 模拟auto_recording_manager的通信部分
def send_signal(signal_type, data=None):
    try:
        message = {
            'session_id': 'mock_session',
            'signal_type': signal_type,
            'timestamp': time.time(),
            'data': data or {}
        }

        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(json.dumps(message).encode('utf-8'), ('127.0.0.1', 12346))
        sock.close()
        print(f"[MOCK] 发送信号: {signal_type}", file=sys.stderr)
    except Exception as e:
        print(f"[MOCK] 发送信号失败: {e}", file=sys.stderr)

def main():
    print("[MOCK] 模拟auto_recording_manager启动", file=sys.stderr)

    # 模拟正常运行一段时间
    time.sleep(2)

    # 模拟控件识别开始
    print("[MOCK] 模拟控件识别开始", file=sys.stderr)
    send_signal('recognition_start', {'x': 500, 'y': 600, 'recognition_type': 'mock'})

    # 模拟长时间阻塞（5秒，超过3秒阈值）
    print("[MOCK] 模拟长时间阻塞（5秒）...", file=sys.stderr)
    for i in range(5):
        time.sleep(1)
        print(f"[MOCK] 阻塞中... {i+1}/5秒", file=sys.stderr)

    # 如果执行到这里，说明没有被杀死
    print("[MOCK] ❌ 没有被监控器杀死！", file=sys.stderr)
    send_signal('recognition_end', {'x': 500, 'y': 600, 'duration': 5.0, 'success': True, 'recognition_type': 'mock'})

if __name__ == "__main__":
    main()
'''

        # 写入模拟脚本
        with open(mock_script, 'w', encoding='utf-8') as f:
            f.write(mock_content)

        # 设置执行权限
        os.chmod(mock_script, 0o755)

        print(f"📝 创建模拟脚本: {mock_script}")

        # 使用监控器监控模拟脚本
        cmd = [
            'python3', str(scripts_dir / 'widget_recognition_monitor.py'),
            '--timeout', '3.0',
            '--debug',
            'python3', str(mock_script)
        ]

        print(f"🚀 启动监控器: {' '.join(cmd)}")

        # 启动监控器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(scripts_dir.parent)
        )

        print(f"✅ 监控器进程已启动，PID: {process.pid}")

        # 等待10秒
        start_time = time.time()
        while time.time() - start_time < 10:
            if process.poll() is not None:
                print(f"⚠️ 监控器进程已退出，返回码: {process.returncode}")
                break

            time.sleep(0.5)

        # 停止进程
        if process.poll() is None:
            print(f"🛑 停止监控器进程...")
            process.terminate()
            try:
                process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()

        # 读取输出
        stdout, stderr = process.communicate()

        print(f"\n📊 测试结果:")
        print(f"   返回码: {process.returncode}")

        if stderr:
            stderr_lines = stderr.strip().split('\n')

            # 查找关键信息
            timeout_found = False
            restart_found = False
            kill_found = False

            for line in stderr_lines:
                if '🚨 控件识别超时' in line:
                    timeout_found = True
                    print(f"   ✅ 发现超时检测: {line}")
                elif '🔄 重启目标进程' in line or '重启完成' in line:
                    restart_found = True
                    print(f"   ✅ 发现进程重启: {line}")
                elif '停止目标进程' in line or 'PID:' in line:
                    kill_found = True
                    print(f"   ✅ 发现进程管理: {line}")

            print(f"\n📈 检测结果:")
            print(f"   超时检测: {'✅' if timeout_found else '❌'}")
            print(f"   进程重启: {'✅' if restart_found else '❌'}")
            print(f"   进程管理: {'✅' if kill_found else '❌'}")

            # 显示最后10行日志
            print(f"\n📝 最后10行日志:")
            for line in stderr_lines[-10:]:
                print(f"   {line}")

            success = timeout_found and (restart_found or kill_found)
        else:
            success = False

        # 清理模拟脚本
        try:
            mock_script.unlink()
        except:
            pass

        return success

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简单通信和超时检测测试")
    print("=" * 80)

    try:
        # 测试1: UDP通信
        success1 = test_udp_communication()

        # 测试2: 监控器与模拟进程
        success2 = test_monitor_with_mock_process()

        print(f"\n✅ 测试完成")
        print(f"📊 测试结果:")
        print(f"   UDP通信测试: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"   监控器进程测试: {'✅ 成功' if success2 else '❌ 失败'}")

        if success1 and success2:
            print(f"\n🎉 所有测试通过！超时检测功能正常工作")
            print(f"💡 现在可以使用以下命令启用超时监控:")
            print(f"   python3 scripts/start_auto_recording_with_lifecycle.py --enable-widget-monitor --widget-timeout 3.0 --debug")
        else:
            print(f"\n⚠️ 部分测试失败，请检查实现")

    except KeyboardInterrupt:
        print(f"\n🛑 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
