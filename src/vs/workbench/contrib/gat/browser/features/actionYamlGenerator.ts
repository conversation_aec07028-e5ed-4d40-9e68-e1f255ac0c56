/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { ILogService } from '../../../../../platform/log/common/log.js';
import { ITextFileService } from '../../../../services/textfile/common/textfiles.js';
import { URI } from '../../../../../base/common/uri.js';
import { OperationRecord } from './operationRecordWindow.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { VSBuffer } from '../../../../../base/common/buffer.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { joinPath } from '../../../../../base/common/resources.js';

/**
 * Action YAML生成器
 * 负责将录制的事件转换为对应的action yaml格式字符串
 */
export class ActionYamlGenerator {
    private generatedActions: string[] = [];
    private currentStepNumber = 1;
    private currentTestCase: any = null;

    constructor(
        private readonly logService: ILogService,
        private readonly textFileService: ITextFileService,
        private readonly instantiationService: IInstantiationService,
        private readonly configurationService: IConfigurationService,
        private readonly fileService: IFileService
    ) { }

    /**
     * 设置当前测试用例信息
     */
    public setCurrentTestCase(testCase: any): void {
        this.currentTestCase = testCase;
        this.logService.info(`ActionYamlGenerator: 设置当前测试用例 - 完整信息: ${JSON.stringify(testCase, null, 2)}`);
    }

    /**
     * 根据操作记录生成对应的action yaml字符串
     */
    public generateActionYaml(operation: OperationRecord): string {
        try {
            this.logService.info(`开始生成Action YAML: ${operation.type} - ${operation.action}`);

            // 过滤不需要写入测试用例的操作
            if (this.shouldSkipOperation(operation)) {
                this.logService.info(`⏭️ 跳过不需要的操作: ${operation.action}`);
                return '';
            }

            let yamlContent = '';

            switch (operation.type) {
                case 'mouse':
                case 'mouse_click':
                case 'mouse_right_click':
                case 'mouse_double_click':
                case 'mouse_drag':
                case 'mouse_hover':
                    yamlContent = this.generateMouseActionYaml(operation);
                    break;
                case 'keyboard':
                    yamlContent = this.generateKeyboardActionYaml(operation);
                    break;
                case 'window':
                    yamlContent = this.generateWindowActionYaml(operation);
                    break;
                case 'menu':
                    // 检查是否是公共方法节点
                    if (operation.details?.isCommonMethod && operation.details?.formattedCode) {
                        yamlContent = this.generateCommonMethodYaml(operation);
                    } else {
                        yamlContent = this.generateGenericActionYaml(operation);
                    }
                    break;
                default:
                    yamlContent = this.generateGenericActionYaml(operation);
                    break;
            }

            if (yamlContent) {
                this.generatedActions.push(yamlContent);
                this.logService.info(`✅ Action YAML生成成功:\n${yamlContent}`);

                // 异步插入locator文件节点
                this.insertLocatorNode(operation).catch(error => {
                    this.logService.error(`插入locator节点失败: ${error}`);
                });
            }

            return yamlContent;
        } catch (error) {
            this.logService.error(`生成Action YAML时出错: ${error}`);
            return '';
        }
    }

    /**
     * 判断是否应该跳过某个操作（不写入测试用例）
     */
    private shouldSkipOperation(operation: OperationRecord): boolean {
        // 1. 跳过状态更新操作
        if (operation.type === 'window' && operation.action === '状态更新') {
            return true;
        }

        // 2. 跳过录制完成事件
        if (operation.type === 'window' && operation.action === '录制完成') {
            return true;
        }

        // 3. 跳过录制统计事件
        if (operation.type === 'window' && operation.action === '录制统计') {
            return true;
        }

        // 4. 跳过录制开始事件
        if (operation.type === 'window' && operation.action === '录制开始') {
            return true;
        }

        // 5. 跳过标记为录制完成的操作
        if (operation.details?.isRecordingComplete) {
            return true;
        }

        // 6. 跳过标记为录制结果的操作
        if (operation.details?.isRecordingResult) {
            return true;
        }

        // 7. 跳过标记为录制开始的操作
        if (operation.details?.isRecordingStart) {
            return true;
        }

        // 4. 跳过旧格式的录制坐标相关的点击操作（带下划线的格式）
        if ((operation.type === 'mouse' || operation.type === 'mouse_click') &&
            operation.details?.key &&
            operation.details.key.startsWith('录制坐标_')) {
            return true;
        }

        // 3. 跳过鼠标操作中使用旧格式录制坐标的操作
        if ((operation.type === 'mouse' || operation.type === 'mouse_click') &&
            operation.target?.name &&
            operation.target.name.includes('录制坐标_')) {
            return true;
        }

        // 4. 跳过内部系统操作
        if (operation.action && (
            operation.action.includes('录制系统') ||
            operation.action.includes('状态更新') ||
            operation.action.includes('Python录制')
        )) {
            return true;
        }

        return false;
    }

    /**
     * 生成鼠标操作的YAML
     */
    private generateMouseActionYaml(operation: OperationRecord): string {
        const details = operation.details;
        const widgetInfo = operation.widget_info;
        const hasValidWidget = widgetInfo && widgetInfo.is_valid && !widgetInfo.error;

        // 判断鼠标操作类型
        let actionName = 'kyrobot_click';

        // 根据操作类型判断Action名称
        if (operation.type === 'mouse_double_click' || operation.action.includes('双击')) {
            actionName = 'kyrobot_doubleclick';
        } else if (operation.type === 'mouse_right_click' || operation.action.includes('右击') || operation.action.includes('右键')) {
            actionName = 'kyrobot_rightclick';
        } else if (operation.type === 'mouse_drag' || operation.action.includes('拖动')) {
            // 拖动事件使用专门的处理方法
            return this.generateMouseDragActionYaml(operation);
        } else if (operation.type === 'mouse_hover' || operation.action.includes('悬浮') || operation.action.includes('悬停')) {
            actionName = 'kyrobot_hover';
        }

        // 记录生成的action名称，便于调试
        this.logService.info(`生成的action名称: ${actionName}, 操作类型: ${operation.type}, 操作描述: ${operation.action}`);


        // 生成时间戳注释
        const timestamp = new Date(operation.timestamp).toISOString();
        let yamlContent = `      # ${timestamp} - ${operation.action}\n`;

        // 获取driver信息，优先使用Python后端提供的匹配结果
        const driver = this.getDriverFromOperation(operation);

        // 记录driver信息，便于调试
        this.logService.info(`当前测试用例信息: ${JSON.stringify(this.currentTestCase)}`);
        this.logService.info(`使用的driver: ${driver}`);

        // 开始构建action
        yamlContent += `      - action: ${actionName}\n`;
        yamlContent += `        driver: ${driver}\n`;

        // 构建kwargs部分
        if (hasValidWidget) {
            // 如果有有效的控件信息，使用UNI方式
            yamlContent += `        kwargs:\n`;

            // 标准化控件信息字段格式
            const normalizedWidgetInfo = this.normalizeWidgetInfoFields(widgetInfo);

            // 优先使用widget_info中的Key字段（标准化后），如果没有则使用控件名称
            // 如果Key字段存在且不为空，直接使用Key字段的值
            let keyValue = normalizedWidgetInfo.Key;
            if (!keyValue || keyValue.trim() === '') {
                // 如果Key字段为空，则使用Name字段，如果Name也为空则使用"未命名控件"
                keyValue = this.sanitizeWidgetName(normalizedWidgetInfo.Name || '未命名控件');
            }
            yamlContent += `          key: ${keyValue}\n`;
            yamlContent += `          type: UNI\n`;

            // 添加控件详细信息作为注释（使用标准化后的字段）
            yamlContent += `          # 控件信息: ${normalizedWidgetInfo.Rolename || '未知类型'}\n`;
            if (normalizedWidgetInfo.ProcessName) {
                yamlContent += `          # 进程: ${normalizedWidgetInfo.ProcessName}\n`;
            }
            if (normalizedWidgetInfo.Coords) {
                yamlContent += `          # 控件区域: (${normalizedWidgetInfo.Coords.x}, ${normalizedWidgetInfo.Coords.y}) ${normalizedWidgetInfo.Coords.width}x${normalizedWidgetInfo.Coords.height}\n`;
            }
            if (normalizedWidgetInfo.WindowName) {
                yamlContent += `          # 窗口名称: ${normalizedWidgetInfo.WindowName}\n`;
            }
        } else {
            // 如果没有控件信息，使用OCR方式
            yamlContent += `        kwargs:\n`;
            yamlContent += `          key: 录制坐标${details.coordinates.x}${details.coordinates.y}\n`;
            yamlContent += `          type: OCR\n`;
            yamlContent += `          # 坐标位置: (${details.coordinates.x}, ${details.coordinates.y})\n`;
        }

        return yamlContent;
    }

    /**
     * 生成鼠标拖动操作的YAML
     */
    private generateMouseDragActionYaml(operation: OperationRecord): string {
        const timestamp = new Date(operation.timestamp).toISOString();
        let yamlContent = `      # ${timestamp} - ${operation.action}\n`;

        // 获取driver信息，优先使用Python后端提供的匹配结果
        const driver = this.getDriverFromOperation(operation);

        // 获取拖动的起始和结束坐标
        const details = operation.details;
        let startX = 0, startY = 0, endX = 0, endY = 0;

        // 从details中获取坐标信息
        if (details) {
            startX = details.start_x || details.start_position?.x || 0;
            startY = details.start_y || details.start_position?.y || 0;
            endX = details.end_x || details.end_position?.x || 0;
            endY = details.end_y || details.end_position?.y || 0;
        }

        // 如果details中没有坐标信息，尝试从target中获取
        if ((startX === 0 && startY === 0) || (endX === 0 && endY === 0)) {
            const target = operation.target;
            if (target && target.position) {
                // 如果只有一个位置信息，可能需要从其他地方获取起始位置
                endX = target.position.x || 0;
                endY = target.position.y || 0;
            }
        }

        this.logService.info(`生成拖动YAML: 起始(${startX}, ${startY}) 结束(${endX}, ${endY})`);

        // 构建kyrobot_drag_a2b格式的YAML
        yamlContent += `      - action: kyrobot_drag_a2b\n`;
        yamlContent += `        driver: ${driver}\n`;
        yamlContent += `        kwargs:\n`;
        yamlContent += `          pos_a: [${startX}, ${startY}]\n`;
        yamlContent += `          pos_b: [${endX}, ${endY}]\n`;

        return yamlContent;
    }

    /**
     * 生成键盘操作的YAML
     */
    private generateKeyboardActionYaml(operation: OperationRecord): string {
        const timestamp = new Date(operation.timestamp).toISOString();
        const isMerged = operation.details?.is_merged || false;
        const mergedCount = operation.details?.merged_count || 1;

        // 为合并事件添加特殊注释
        let yamlContent = `      # ${timestamp} - ${operation.action}`;
        if (isMerged) {
            yamlContent += ` (合并了${mergedCount}个按键)`;
        }
        yamlContent += `\n`;

        // 获取driver信息，优先使用Python后端提供的匹配结果
        const driver = this.getDriverFromOperation(operation);

        // 优先使用key_combination（组合键），如果没有则使用单个key
        const keyName = operation.details?.key_combination || operation.details?.key || operation.target?.text || '';

        // 记录调试信息
        this.logService.info(`🔍 键盘事件详情: key=${operation.details?.key}, key_combination=${operation.details?.key_combination}, 最终使用: ${keyName}, 是否合并: ${isMerged}`);

        // 对于合并的键盘事件，使用kyrobot_input_string而不是kyrobot_type
        if (isMerged && keyName.length > 1) {
            yamlContent += `      - action: kyrobot_input_string\n`;
            yamlContent += `        driver: ${driver}\n`;
            yamlContent += `        kwargs:\n`;
            yamlContent += `          type: OCR\n`;
            yamlContent += `          text: "${keyName}"\n`;
        } else {
            yamlContent += `      - action: kyrobot_type\n`;
            yamlContent += `        driver: ${driver}\n`;
            yamlContent += `        kwargs:\n`;
            yamlContent += `          type: OCR\n`;

            // 检查是否是数字，如果是数字则添加引号
            const formattedKey = this.formatKeyValue(keyName);
            yamlContent += `          key: ${formattedKey}\n`;
        }

        return yamlContent;
    }

    /**
     * 格式化按键值，为数字和符号添加引号
     */
    private formatKeyValue(keyValue: string): string {
        // 检查是否是纯数字（包括小数）
        if (this.isNumericString(keyValue)) {
            return `"${keyValue}"`;
        }

        // 检查是否是纯数字组合（如 "123", "456"）
        if (/^\d+$/.test(keyValue)) {
            return `"${keyValue}"`;
        }

        // 检查是否是符号字符
        if (this.isSymbolString(keyValue)) {
            return `"${keyValue}"`;
        }

        // 其他情况保持原样
        return keyValue;
    }

    /**
     * 判断字符串是否是数字
     */
    private isNumericString(str: string): boolean {
        // 检查是否是纯数字字符串
        return /^\d+(\.\d+)?$/.test(str);
    }

    /**
     * 判断字符串是否是符号
     */
    private isSymbolString(str: string): boolean {
        // 常见的符号字符，包括标点符号、运算符、特殊字符等
        const symbolPattern = /^[!@#$%^&*()_+\-=\[\]{}|;':",./<>?`~\\]+$/;
        return symbolPattern.test(str);
    }

    /**
     * 生成窗口操作的YAML
     */
    private generateWindowActionYaml(operation: OperationRecord): string {
        const timestamp = new Date(operation.timestamp).toISOString();
        let yamlContent = `      # ${timestamp} - ${operation.action}\n`;

        // 获取driver信息，优先使用Python后端提供的匹配结果
        const driver = this.getDriverFromOperation(operation);

        yamlContent += `      - action: kyrobot_window_operation\n`;
        yamlContent += `        driver: ${driver}\n`;
        yamlContent += `        kwargs:\n`;
        yamlContent += `          operation: "${operation.action}"\n`;

        return yamlContent;
    }

    /**
     * 生成公共方法的YAML
     */
    private generateCommonMethodYaml(operation: OperationRecord): string {
        const timestamp = new Date(operation.timestamp).toISOString();
        let yamlContent = `      # ${timestamp} - ${operation.action}\n`;

        // 直接使用预先格式化好的代码，添加适当的缩进
        const formattedCode = operation.details.formattedCode;
        if (formattedCode) {
            // 将格式化的代码按行分割，并为每行添加适当的缩进
            const lines = formattedCode.split('\n');
            for (const line of lines) {
                if (line.trim()) {
                    yamlContent += `      ${line}\n`;
                }
            }
        } else {
            // 如果没有格式化代码，回退到通用方法
            this.logService.warn(`公共方法节点缺少格式化代码: ${operation.details.methodName}`);
            return this.generateGenericActionYaml(operation);
        }

        this.logService.info(`✅ 生成公共方法YAML: ${operation.details.methodName}`);
        return yamlContent;
    }

    /**
     * 生成通用操作的YAML
     */
    private generateGenericActionYaml(operation: OperationRecord): string {
        const timestamp = new Date(operation.timestamp).toISOString();
        let yamlContent = `      # ${timestamp} - ${operation.action}\n`;

        // 获取driver信息，优先使用Python后端提供的匹配结果
        const driver = this.getDriverFromOperation(operation);

        yamlContent += `      - action: kyrobot_generic_action\n`;
        yamlContent += `        driver: ${driver}\n`;
        yamlContent += `        kwargs:\n`;
        yamlContent += `          description: "${operation.action}"\n`;

        return yamlContent;
    }

    /**
     * 清理控件名称，确保YAML安全
     */
    private sanitizeWidgetName(name: string): string {
        // 移除特殊字符，保留中文、英文、数字和下划线
        return name.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_]/g, '_');
    }

    /**
     * 从测试用例中获取driver信息
     */
    private getDriverFromTestCase(): string {
        let driver = 'unknown_app';
        let driverSource = 'default';

        // 详细的调试日志
        this.logService.info(`🔍 开始获取driver信息...`);
        this.logService.info(`📋 当前测试用例ID: ${this.currentTestCase?.id || this.currentTestCase?.TestCaseID || 'unknown'}`);
        this.logService.info(`📋 TestCaseAppList: ${JSON.stringify(this.currentTestCase?.TestCaseAppList)}`);
        this.logService.info(`📋 driver字段: ${this.currentTestCase?.driver}`);

        // 尝试从不同字段获取driver信息
        if (this.currentTestCase?.TestCaseAppList) {
            // 如果是数组，取第一个元素
            if (Array.isArray(this.currentTestCase.TestCaseAppList) && this.currentTestCase.TestCaseAppList.length > 0) {
                const originalDriver = this.currentTestCase.TestCaseAppList[0];
                driver = originalDriver;
                driverSource = 'TestCaseAppList[0]';
                this.logService.info(`📋 从TestCaseAppList数组获取: ${originalDriver}`);

                // 如果包含路径分隔符，取最后一部分作为应用名称
                if (driver.includes('/')) {
                    const pathParts = driver.split('/');
                    driver = pathParts.pop() || driver;
                    this.logService.info(`📋 路径处理: ${originalDriver} → ${driver}`);
                }
            } else if (typeof this.currentTestCase.TestCaseAppList === 'string') {
                const originalDriver = this.currentTestCase.TestCaseAppList;
                driver = originalDriver;
                driverSource = 'TestCaseAppList(string)';
                this.logService.info(`📋 从TestCaseAppList字符串获取: ${originalDriver}`);

                // 处理逗号分隔的字符串，取第一个应用
                if (driver.includes(',')) {
                    const commaParts = driver.split(',');
                    driver = commaParts[0].trim();
                    this.logService.info(`📋 逗号分隔处理: ${originalDriver} → ${driver}`);
                }
                // 如果包含路径分隔符，取最后一部分作为应用名称
                if (driver.includes('/')) {
                    const pathParts = driver.split('/');
                    driver = pathParts.pop() || driver;
                    this.logService.info(`📋 路径处理: ${originalDriver} → ${driver}`);
                }
            }
        } else if (this.currentTestCase?.driver) {
            driver = this.currentTestCase.driver;
            driverSource = 'driver字段';
            this.logService.info(`📋 从driver字段获取: ${driver}`);
        }

        this.logService.info(`✅ 最终driver值: ${driver} (来源: ${driverSource})`);
        return driver;
    }

    /**
     * 从操作记录中获取driver信息，优先使用Python后端提供的匹配结果
     */
    private getDriverFromOperation(operation: OperationRecord): string {
        let driver = 'unknown_app';
        let driverSource = 'default';

        // 优先使用Python后端在widget_info中提供的driver字段
        if (operation.widget_info?.driver) {
            driver = operation.widget_info.driver;
            driverSource = 'widget_info.driver (Python后端匹配)';
            this.logService.info(`🎯 使用Python后端匹配的driver: ${driver}`);
        }
        // 如果没有Python后端的driver信息，回退到测试用例信息
        else {
            driver = this.getDriverFromTestCase();
            driverSource = '测试用例信息';
            this.logService.info(`📋 回退到测试用例driver: ${driver}`);
        }

        this.logService.info(`✅ 最终使用driver: ${driver} (来源: ${driverSource})`);
        return driver;
    }

    /**
     * 生成完整的测试步骤YAML
     */
    public generateStepYaml(stepName?: string): string {
        if (this.generatedActions.length === 0) {
            // 当没有生成的actions时，返回默认的步骤1
            return '    - 步骤1：请录制步骤:\n';
        }

        const stepTitle = stepName || `步骤${this.currentStepNumber}：录制操作`;
        let stepYaml = `    - ${stepTitle}:\n`;

        // 添加所有生成的actions
        for (const action of this.generatedActions) {
            stepYaml += action;
        }

        this.currentStepNumber++;
        return stepYaml;
    }

    /**
     * 清空已生成的actions
     */
    public clearActions(): void {
        this.generatedActions = [];
    }

    /**
     * 获取所有已生成的actions
     */
    public getGeneratedActions(): string[] {
        return [...this.generatedActions];
    }

    /**
     * 保存生成的YAML到文件
     */
    public async saveToFile(filePath: URI, content: string): Promise<void> {
        try {
            // 方法1: 尝试使用textFileService.write
            try {
                await this.textFileService.write(filePath, content);
                this.logService.info(`✅ YAML文件保存成功: ${filePath.toString()}`);
                return;
            } catch (textServiceError) {
                this.logService.warn(`textFileService.write失败，尝试备用方法: ${textServiceError}`);

                // 方法2: 尝试使用fileService.writeFile
                const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
                const data = VSBuffer.fromString(content);

                try {
                    await fileService.writeFile(filePath, data);
                    this.logService.info(`✅ YAML文件保存成功（使用fileService）: ${filePath.toString()}`);
                    return;
                } catch (fileServiceError) {
                    this.logService.warn(`fileService.writeFile也失败: ${fileServiceError}`);

                    // 方法3: 创建临时文件然后移动
                    try {
                        const tempPath = filePath.with({ path: filePath.path + '.tmp' });
                        await fileService.writeFile(tempPath, data);

                        // 尝试删除原文件
                        try {
                            await fileService.del(filePath, { useTrash: false });
                        } catch (delError) {
                            this.logService.warn(`删除原文件失败，继续尝试移动: ${delError}`);
                        }

                        // 移动临时文件到目标位置
                        await fileService.move(tempPath, filePath);
                        this.logService.info(`✅ YAML文件保存成功（使用临时文件方法）: ${filePath.toString()}`);
                        return;
                    } catch (tempFileError) {
                        this.logService.error(`临时文件方法也失败: ${tempFileError}`);
                        throw new Error(`所有文件写入方法都失败了。最后的错误: ${tempFileError}`);
                    }
                }
            }
        } catch (error) {
            this.logService.error(`保存YAML文件失败: ${error}`);
            throw error;
        }
    }



    /**
     * 生成完整的测试用例YAML模板
     */
    public generateTestCaseTemplate(testCaseId: string, feature: string, story: string): string {
        const template = `${testCaseId}:
  feature: "${feature}"
  story: "${story}"
  modelpath: "录制生成/自动录制"
  author: kylinrobot
  label: UNI
  setup:
  teardown:
  steps:
${this.generateStepYaml()}
`;
        return template;
    }

    /**
     * 插入locator节点到locator文件中
     */
    private async insertLocatorNode(operation: OperationRecord): Promise<void> {
        try {
            // 只处理鼠标操作
            if (!operation.type.startsWith('mouse')) {
                return;
            }

            // 获取driver信息，优先使用Python后端提供的匹配结果
            const driver = this.getDriverFromOperation(operation);
            if (!driver) {
                this.logService.warn('无法获取driver信息，跳过locator节点插入');
                return;
            }

            // 获取key信息
            const keyInfo = this.extractKeyFromOperation(operation);
            if (!keyInfo) {
                this.logService.info('操作无需插入locator节点');
                return;
            }

            this.logService.info(`准备插入locator节点: key=${keyInfo.key}, type=${keyInfo.type}, driver=${driver}`);

            // 根据不同类型插入locator节点
            switch (keyInfo.type) {
                case 'UNI':
                    await this.insertUniLocatorNode(keyInfo.key, keyInfo.widgetInfo, driver);
                    break;
                case 'OCR':
                    if (keyInfo.coordinates) {
                        await this.insertOcrLocatorNode(keyInfo.key, keyInfo.coordinates, driver);
                    }
                    break;
                case 'POSITION':
                    if (keyInfo.coordinates) {
                        await this.insertPositionLocatorNode(keyInfo.key, keyInfo.coordinates, driver);
                    }
                    break;
                default:
                    this.logService.info(`不支持的locator类型: ${keyInfo.type}`);
                    break;
            }

        } catch (error) {
            this.logService.error(`插入locator节点时出错: ${error}`);
        }
    }

    /**
     * 从操作记录中提取key信息
     */
    private extractKeyFromOperation(operation: OperationRecord): { key: string; type: string; widgetInfo?: any; coordinates?: { x: number; y: number } } | null {
        // 从YAML内容中提取key和type信息
        const yamlContent = this.generateMouseActionYaml(operation);

        // 解析key
        const keyMatch = yamlContent.match(/key:\s*(.+)/);
        if (!keyMatch) {
            return null;
        }
        const key = keyMatch[1].trim();

        // 解析type
        const typeMatch = yamlContent.match(/type:\s*(.+)/);
        if (!typeMatch) {
            return null;
        }
        const type = typeMatch[1].trim();

        // 根据类型返回不同的信息
        const result: any = { key, type };

        if (type === 'UNI' && operation.widget_info) {
            result.widgetInfo = operation.widget_info;
        } else if ((type === 'OCR' || type === 'POSITION') && operation.details?.coordinates) {
            result.coordinates = operation.details.coordinates;
        }

        return result;
    }

    /**
     * 插入UNI类型的locator节点
     */
    private async insertUniLocatorNode(key: string, widgetInfo: any, driver: string): Promise<void> {
        try {
            if (!widgetInfo) {
                this.logService.warn('UNI控件信息为空，跳过locator节点插入');
                return;
            }

            // 获取测试用例路径配置
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePathFromConfig) {
                this.logService.warn('未配置 gat.testcasePath，跳过locator节点插入');
                return;
            }

            // 确定locator目录路径（与testcase同级）
            const locatorDirPath = joinPath(URI.file(testCasePathFromConfig), '..', 'locator');

            // 确保locator目录存在
            await this.fileService.createFolder(locatorDirPath);

            // 构建文件名
            const fileName = `${driver}_uni.yml`;  // 文件名使用下划线分隔
            const filePath = joinPath(locatorDirPath, fileName);

            // 创建YAML格式的内容
            const rawDatamap = widgetInfo.datamap || widgetInfo;
            const datamap = this.normalizeWidgetInfoFields(rawDatamap);
            const datamapFormatted = this.formatObjectAsJsonStyle(datamap, 2);
            const yamlContent = `${key}:\n  datamap: ${datamapFormatted}\n`;

            // 检查文件是否已存在并追加内容
            let finalContent = yamlContent;
            if (await this.fileService.exists(filePath)) {
                const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                if (!existingContent.includes(key + ':')) {
                    finalContent = existingContent + '\n' + yamlContent;
                } else {
                    this.logService.info(`UNI控件 ${key} 已存在于 ${fileName} 中，不再追加`);
                    return;
                }
            }

            // 写入文件
            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.logService.info(`✅ 已插入UNI locator节点: ${key} 到 ${fileName}`);

        } catch (error) {
            this.logService.error(`插入UNI locator节点失败: ${error}`);
        }
    }

    /**
     * 插入OCR类型的locator节点
     */
    private async insertOcrLocatorNode(key: string, coordinates: { x: number; y: number }, driver: string): Promise<void> {
        try {
            // 获取测试用例路径配置
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePathFromConfig) {
                this.logService.warn('未配置 gat.testcasePath，跳过locator节点插入');
                return;
            }

            // 确定locator目录路径
            const locatorDirPath = joinPath(URI.file(testCasePathFromConfig), '..', 'locator');

            // 确保locator目录存在
            await this.fileService.createFolder(locatorDirPath);

            // 构建文件名
            const fileName = `${driver}.yml`;
            const filePath = joinPath(locatorDirPath, fileName);

            // 创建OCR类型的YAML内容
            const yamlContent = `${key}:\n  type: ocr\n  position:\n    x: ${coordinates.x}\n    y: ${coordinates.y}\n`;

            // 检查文件是否已存在并追加内容
            let finalContent = yamlContent;
            if (await this.fileService.exists(filePath)) {
                const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                if (!existingContent.includes(key + ':')) {
                    finalContent = existingContent + '\n' + yamlContent;
                } else {
                    this.logService.info(`OCR节点 ${key} 已存在于 ${fileName} 中，不再追加`);
                    return;
                }
            }

            // 写入文件
            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.logService.info(`✅ 已插入OCR locator节点: ${key} 到 ${fileName}`);

        } catch (error) {
            this.logService.error(`插入OCR locator节点失败: ${error}`);
        }
    }

    /**
     * 插入POSITION类型的locator节点
     */
    private async insertPositionLocatorNode(key: string, coordinates: { x: number; y: number }, driver: string): Promise<void> {
        try {
            // 获取测试用例路径配置
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePathFromConfig) {
                this.logService.warn('未配置 gat.testcasePath，跳过locator节点插入');
                return;
            }

            // 确定locator目录路径
            const locatorDirPath = joinPath(URI.file(testCasePathFromConfig), '..', 'locator');

            // 确保locator目录存在
            await this.fileService.createFolder(locatorDirPath);

            // 构建文件名
            const fileName = `${driver}.yml`;
            const filePath = joinPath(locatorDirPath, fileName);

            // 创建POSITION类型的YAML内容
            const yamlContent = `${key}:\n  type: position\n  position:\n    x: ${coordinates.x}\n    y: ${coordinates.y}\n`;

            // 检查文件是否已存在并追加内容
            let finalContent = yamlContent;
            if (await this.fileService.exists(filePath)) {
                const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                if (!existingContent.includes(key + ':')) {
                    finalContent = existingContent + '\n' + yamlContent;
                } else {
                    this.logService.info(`POSITION节点 ${key} 已存在于 ${fileName} 中，不再追加`);
                    return;
                }
            }

            // 写入文件
            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.logService.info(`✅ 已插入POSITION locator节点: ${key} 到 ${fileName}`);

        } catch (error) {
            this.logService.error(`插入POSITION locator节点失败: ${error}`);
        }
    }

    /**
     * 标准化控件信息字段格式
     * 将错误的小写字段名转换为正确的首字母大写格式
     */
    private normalizeWidgetInfoFields(widgetInfo: any): any {
        if (!widgetInfo || typeof widgetInfo !== 'object') {
            return widgetInfo;
        }

        // 字段名映射表：错误格式 -> 正确格式
        const fieldMapping: { [key: string]: string } = {
            'name': 'Name',
            'role': 'Rolename',
            'description': 'Description',
            'process_name': 'ProcessName',
            'process_id': 'ProcessID',
            'coords': 'Coords',
            'actions': 'Actions',
            'states': 'States',
            'text': 'Text',
            'id': 'ID',
            'children_count': 'ChildrenCount',
            'index_in_parent': 'Index_in_parent',
            'parent_path': 'ParentPath',
            'parent_count': 'ParentCount',
            'window_name': 'WindowName',
            'window_role': 'WindowRoleName',
            'window_child_count': 'WindowChildCount',
            'record_position': 'RecordPosition',
            'key': 'Key',
            'menu_element': 'MenuElement'
        };

        const normalized: any = {};

        // 遍历所有字段，进行映射转换
        for (const [key, value] of Object.entries(widgetInfo)) {
            const normalizedKey = fieldMapping[key] || key; // 如果没有映射，保持原字段名
            normalized[normalizedKey] = value;
        }

        return normalized;
    }

    /**
     * 格式化对象为标准YAML格式的字符串（符合UNI locator标准）
     */
    private formatObjectAsJsonStyle(obj: any, indent: number = 0): string {
        if (obj === null || obj === undefined) {
            return 'null';
        }

        if (typeof obj === 'string') {
            // 字符串需要添加引号，符合标准格式
            return `"${obj}"`;
        }

        if (typeof obj === 'number' || typeof obj === 'boolean') {
            return String(obj);
        }

        if (Array.isArray(obj)) {
            if (obj.length === 0) {
                return '[]';
            }

            // 对于数组，使用标准YAML格式
            const indentStr = ' '.repeat(indent);
            const nextIndentStr = ' '.repeat(indent + 2);

            if (obj.every(item => typeof item === 'number' || typeof item === 'string')) {
                // 简单类型数组，使用紧凑格式
                const items = obj.map(item => this.formatObjectAsJsonStyle(item, 0));
                return `[\n${nextIndentStr}${items.join(',\n' + nextIndentStr)},\n${indentStr}]`;
            } else {
                // 复杂类型数组，使用展开格式
                const items = obj.map(item => this.formatObjectAsJsonStyle(item, indent + 2));
                return `[\n${nextIndentStr}${items.join(',\n' + nextIndentStr)},\n${indentStr}]`;
            }
        }

        if (typeof obj === 'object') {
            const entries = Object.entries(obj);
            if (entries.length === 0) {
                return '{}';
            }

            const indentStr = ' '.repeat(indent);
            const nextIndentStr = ' '.repeat(indent + 2);

            const formattedEntries = entries.map(([key, value]) => {
                const formattedValue = this.formatObjectAsJsonStyle(value, indent + 2);
                return `${nextIndentStr}${key}: ${formattedValue}`;
            });

            return `{\n${formattedEntries.join(',\n')}\n${indentStr}}`;
        }

        return String(obj);
    }
}
