/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../../nls.js';

/**
 * 元素捕获管理器
 * 负责处理元素捕获功能，允许用户通过点击界面元素来获取坐标
 */
export class ElementCaptureManager extends Disposable {
    private isCapturing: boolean = false;
    private captureCallback: ((x: number, y: number) => void) | null = null;
    private originalCursor: string = '';

    constructor(
        @ILogService private readonly logService: ILogService,
        @INotificationService private readonly notificationService: INotificationService
    ) {
        super();
    }

    /**
     * 开始捕获元素
     */
    public startCapture(callback: (x: number, y: number) => void): void {
        if (this.isCapturing) {
            this.stopCapture();
        }

        this.isCapturing = true;
        this.captureCallback = callback;

        // 保存原始光标样式
        this.originalCursor = document.body.style.cursor;
        
        // 设置光标为十字形
        document.body.style.cursor = 'crosshair';
        
        // 添加点击事件监听器
        document.addEventListener('click', this.handleClick);
        
        // 添加ESC键监听器取消捕获
        document.addEventListener('keydown', this.handleKeyDown);
        
        this.logService.info('开始元素捕获模式');
        this.notificationService.info(localize('startCapturingElement', "请点击要捕获的元素，按ESC取消"));
    }

    /**
     * 停止捕获元素
     */
    public stopCapture(): void {
        if (!this.isCapturing) {
            return;
        }

        // 恢复原始光标样式
        document.body.style.cursor = this.originalCursor;
        
        // 移除事件监听器
        document.removeEventListener('click', this.handleClick);
        document.removeEventListener('keydown', this.handleKeyDown);
        
        this.isCapturing = false;
        this.captureCallback = null;
        
        this.logService.info('结束元素捕获模式');
    }

    /**
     * 处理点击事件
     */
    private handleClick = (e: MouseEvent): void => {
        // 阻止事件传播
        e.preventDefault();
        e.stopPropagation();
        
        if (this.captureCallback) {
            // 获取点击坐标
            const x = e.screenX;
            const y = e.screenY;
            
            // 调用回调函数
            this.captureCallback(x, y);
        }
        
        // 停止捕获
        this.stopCapture();
    };

    /**
     * 处理键盘事件
     */
    private handleKeyDown = (e: KeyboardEvent): void => {
        // 如果按下ESC键，取消捕获
        if (e.key === 'Escape') {
            e.preventDefault();
            this.stopCapture();
            this.notificationService.info(localize('captureCancelled', "已取消元素捕获"));
        }
    };

    /**
     * 检查是否正在捕获
     */
    public isCapturingNow(): boolean {
        return this.isCapturing;
    }

    override dispose(): void {
        this.stopCapture();
        super.dispose();
    }
}
