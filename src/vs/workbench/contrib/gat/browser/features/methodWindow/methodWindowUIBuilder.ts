/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2024-2025 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../../../platform/log/common/log.js';
import { MethodDataLoader } from './methodDataLoader.js';
import { ThemeManager } from './themeManager.js';
// import { localize } from '../../../../../../nls.js';

/**
 * 负责创建和管理窗口 UI
 */
export class MethodWindowUIBuilder extends Disposable {
    constructor(
        private readonly methodDataLoader: MethodDataLoader,
        private readonly themeManager: ThemeManager,
        @ILogService private readonly logService: ILogService
    ) {
        super();
    }

    /**
     * 测试用例上下文
     */
    private testCaseContext: any = {};
    // 应用对象列表字段已移除，由事件处理器管理

    /**
     * 新增：记录选中的节点，默认 setup
     */
    private selectedSection: string = 'setup';

    /**
     * 编辑模式
     */
    private editMode: boolean = false;
    private editedMethodName: string = '';

    /**
     * 设置测试用例上下文
     */
    public setTestCaseContext(ctx: any): void {
        this.testCaseContext = ctx;
        this.logService.info(`设置测试用例上下文: ${JSON.stringify(ctx)}`);
    }

    /**
     * 设置编辑模式上下文，仅展示不可编辑界面
     */
    public setEditMethodContext(context: { methodName: string }): void {
        this.editMode = true;
        this.editedMethodName = context.methodName;
    }

    /**
     * 清除编辑模式，恢复普通插入模式
     */
    public clearEditMethodContext(): void {
        this.editMode = false;
        this.editedMethodName = '';
    }

    /**
     * 创建窗口内容
     */
    public createWindowContent(doc: Document): void {
        // 清除所有现有内容，包括加载提示
        this.clearDocumentContent(doc);

        // 添加元数据
        this.addMetadata(doc);

        // 添加样式
        this.addStyles(doc);

        // 应用当前主题
        this.themeManager.applyThemeToWindow(doc);

        // 使用安全的方式创建内容
        this.createContentSafely(doc);

        // 添加状态提示栏到主容器底部
        const statusDiv = doc.createElement('div');
        statusDiv.id = 'insert-status';
        statusDiv.className = 'status-bar';
        const container = doc.querySelector('.container') as HTMLElement | null;
        if (container) {
            container.appendChild(statusDiv);
        }
    }

    /**
     * 清除文档内容
     */
    private clearDocumentContent(doc: Document): void {
        // 保存必要的元数据
        const title = doc.title;

        // 清除head内容
        while (doc.head.firstChild) {
            doc.head.removeChild(doc.head.firstChild);
        }

        // 清除body内容
        while (doc.body.firstChild) {
            doc.body.removeChild(doc.body.firstChild);
        }

        // 恢复标题
        doc.title = title;
    }

    /**
     * 添加元数据
     */
    private addMetadata(doc: Document): void {
        // 设置窗口标题
        doc.title = '插入公共方法';

        // 添加视口元数据
        const meta = doc.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        doc.head.appendChild(meta);
    }

    /**
     * 添加样式
     */
    private addStyles(doc: Document): void {
        // 添加主题样式
        const themeStyle = doc.createElement('style');
        themeStyle.textContent = this.themeManager.getStyles();
        doc.head.appendChild(themeStyle);

        // 添加组件样式
        const style = doc.createElement('style');
        style.textContent = `
/* 主容器 */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--foreground);
    background-color: var(--background);
    overflow: hidden;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--header-background);
    color: var(--header-foreground);
    user-select: none;
    cursor: move;
    border-bottom: 1px solid var(--border-color);
}

.header-title {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    color: var(--header-foreground);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 内容区域 */
.content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧面板 */
.sidebar {
    width: 250px;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.search-container {
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
}

.search-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--input-foreground);
}

.method-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 0 10px 0;
}

.category-header {
    padding: 8px 10px;
    font-weight: 500;
    background-color: var(--list-hover-background);
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-header::after {
    content: '▼';
    font-size: 10px;
    transition: transform 0.2s;
}

.category-header.collapsed::after {
    transform: rotate(-90deg);
}

.methods-container {
    max-height: 500px;
    overflow-y: auto;
    transition: max-height 0.3s;
}

.methods-container.collapsed {
    max-height: 0;
    overflow: hidden;
}

.method-item {
    padding: 6px 10px 6px 20px;
    cursor: pointer;
    transition: background-color 0.1s;
}

.method-item:hover {
    background-color: var(--list-hover-background);
}

.method-item.selected {
    background-color: var(--list-active-background);
    color: var(--list-active-foreground);
}

.method-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* 右侧内容区域 */
.main-content {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    position: relative;
}

.content-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--foreground-secondary);
    font-style: italic;
}

.method-details {
    display: none;
}

.method-title {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--foreground);
}

.description-box {
    margin-bottom: 20px;
    line-height: 1.5;
}

.label {
    font-weight: 500;
}

/* 参数表单样式 */
.parameters-section {
    margin-top: 20px;
}

.parameters-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
    padding-right: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.03);
}

.param-label {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.required-mark {
    color: #e51400;
}

.tooltip-icon {
    color: #888;
    cursor: help;
    font-size: 12px;
}

.form-input, .form-select {
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--input-foreground);
    font-size: 13px;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--focus-border);
}

.checkbox-container {
    display: flex;
    align-items: center;
}

.form-checkbox {
    margin-right: 5px;
    width: 16px;
    height: 16px;
}

.default-value-hint {
    font-size: 12px;
    color: #888;
    margin-top: 2px;
}

/* Key input container styles */
.key-input-container {
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%;
    align-items: center;
}

.key-input {
    flex: 3; /* 文本输入框占据更多空间 */
}

.key-type-select {
    flex: 2; /* 下拉菜单占据中等空间 */
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--input-foreground);
    font-size: 13px;
}

.key-type-select:focus {
    outline: none;
    border-color: var(--focus-border);
}

.position-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.position-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.position-label {
    min-width: 30px;
}

.position-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--input-foreground);
}

.capture-button {
    flex: 1; /* 按钮占据最少空间 */
    padding: 6px 12px;
    background-color: var(--button-background);
    color: var(--button-foreground);
    border: 1px solid var(--border-color);
    border-radius: 3px;
    cursor: pointer;
    white-space: nowrap;
    min-width: 90px; /* 确保按钮有最小宽度 */
}

.capture-button:hover {
    background-color: var(--button-hover-background);
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.radio-container {
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-radio {
    margin: 0;
}

.no-params-message {
    color: #888;
    font-style: italic;
    padding: 10px 0;
}

/* 示例区域样式 */
.examples-section {
    margin-top: 20px;
}

.examples-content {
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 12px;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    margin: 10px 0;
    max-height: 150px;
    overflow-y: auto;
}

/* 底部工具栏 */
.footer {
    display: flex;
    align-items: center; /* 新增垂直居中 */
    justify-content: flex-end;
    padding: 10px 15px;
    border-top: 1px solid var(--border-color);
    gap: 10px;
}

.footer-button {
    padding: 6px 12px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 13px;
}

.cancel-button {
    background-color: var(--button-secondary-background);
    color: var(--button-secondary-foreground);
}

.cancel-button:hover {
    background-color: var(--button-secondary-hover-background);
}

.insert-button {
    background-color: var(--button-primary-background);
    color: var(--button-primary-foreground);
}

.insert-button:hover {
    background-color: var(--button-primary-hover-background);
}

/* 用例步骤下拉框 */
.step-selector-container {
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
}
.step-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--input-foreground);
}

/* 新增：元素对象输入下拉样式 */
.object-input-container {
    position: relative;
    width: 100%;
}
.object-input {
    width: 100%;
    box-sizing: border-box;
}
.object-dropdown {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: var(--input-background);
    opacity: 1;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    z-index: 1000;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    display: none;
}
.object-dropdown-item {
    padding: 6px 8px;
    cursor: pointer;
}
.object-dropdown-item:hover {
    background-color: var(--list-hover-background);
}

/* 新增：美化底部"连续插入"复选框 */
.footer-checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0;
}
.form-checkbox {
    width: 16px;
    height: 16px;
    margin: 0;
}

/* 列表输入样式 */
.list-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.list-items-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
}
.list-item-group {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 插入状态提示栏 */
.status-bar {
    padding: 8px;
    text-align: center;
    border-top: 1px solid var(--border-color);
    color: var(--foreground);
}
        `;
        doc.head.appendChild(style);
    }

    /**
     * 使用安全的方式创建内容
     * 通过逐个创建元素而不是使用innerHTML
     */
    private createContentSafely(doc: Document): void {
        try {
            // 创建主容器
            const container = doc.createElement('div');
            container.className = 'container';
            doc.body.appendChild(container);

            // 创建标题栏
            this.createHeader(doc, container);

            // 创建内容区域
            const content = doc.createElement('div');
            content.className = 'content';
            container.appendChild(content);

            // 创建左侧面板
            const sidebar = doc.createElement('div');
            sidebar.className = 'sidebar';
            content.appendChild(sidebar);

            // 创建用例步骤下拉框
            this.createStepSelector(doc, sidebar);

            // 创建搜索框
            this.createSearchBox(doc, sidebar);

            // 创建方法列表
            this.createMethodList(doc, sidebar);

            // 创建右侧内容区域
            const mainContent = doc.createElement('div');
            mainContent.className = 'main-content';
            content.appendChild(mainContent);

            // 创建方法详情区域
            this.createMethodDetails(doc, mainContent);

            // 创建底部工具栏
            this.createFooter(doc, container);

            this.logService.info('窗口内容创建成功');
        } catch (error) {
            this.logService.error('创建窗口内容时出错', error);
        }
    }

    /**
     * 创建标题栏
     */
    private createHeader(doc: Document, container: HTMLElement): void {
        const header = doc.createElement('div');
        header.className = 'header';

        const headerTitle = doc.createElement('h1');
        headerTitle.className = 'header-title';
        headerTitle.textContent = '插入公共方法';
        header.appendChild(headerTitle);

        const closeButton = doc.createElement('button');
        closeButton.className = 'close-button';
        closeButton.textContent = '×';
        closeButton.title = '关闭';
        header.appendChild(closeButton);

        container.appendChild(header);
    }

    /**
     * 创建用例步骤下拉框
     */
    private createStepSelector(doc: Document, container: HTMLElement): void {
        const stepContainer = doc.createElement('div');
        stepContainer.className = 'step-selector-container';
        const label = doc.createElement('label');
        label.textContent = '用例步骤\uff1a';
        label.htmlFor = 'step-select';
        stepContainer.appendChild(label);
        const select = doc.createElement('select');
        select.id = 'step-select';
        select.className = 'step-select';
        // 添加 setup 和 teardown 选项
        ['setup', 'teardown'].forEach(section => {
            const option = doc.createElement('option');
            option.value = section;
            option.textContent = section;
            select.appendChild(option);
        });
        // 添加具体的测试用例步骤
        if (Array.isArray(this.testCaseContext.TestCaseSteps)) {
            this.testCaseContext.TestCaseSteps.forEach((step: any) => {
                const option = doc.createElement('option');
                const stepLabel = `步骤${step.index}\uff1a${step.desc}`;
                option.value = stepLabel;
                option.textContent = stepLabel;
                select.appendChild(option);
            });
        }
        // 添加整体 steps 选项
        {
            const option = doc.createElement('option');
            option.value = 'steps';
            option.textContent = 'steps';
            select.appendChild(option);
        }
        // 监听用户选择，更新 selectedSection
        select.addEventListener('change', () => {
            this.selectedSection = select.value;
        });
        stepContainer.appendChild(select);
        container.appendChild(stepContainer);
    }

    /**
     * 创建搜索框
     */
    private createSearchBox(doc: Document, container: HTMLElement): void {
        const searchContainer = doc.createElement('div');
        searchContainer.className = 'search-container';

        const searchInput = doc.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = '搜索方法...';
        if (this.editMode) { searchInput.disabled = true; }

        searchContainer.appendChild(searchInput);
        container.appendChild(searchContainer);
    }

    /**
     * 创建方法列表
     */
    private createMethodList(doc: Document, container: HTMLElement): void {
        const methodList = doc.createElement('div');
        methodList.className = 'method-list';

        // 获取方法分类
        const categories = this.methodDataLoader.getMethodCategories();

        // 如果没有方法数据，显示加载中
        if (categories.size === 0) {
            const loadingMsg = doc.createElement('div');
            loadingMsg.className = 'loading-message';
            loadingMsg.textContent = '加载方法数据中...';
            methodList.appendChild(loadingMsg);
            container.appendChild(methodList);
            return;
        }

        // 按顺序创建分类
        const sortedCategories = Array.from(categories.entries())
            .sort((a, b) => {
                // 按照预定义顺序排序
                const order: Record<string, number> = {
                    '基础方法': 1,
                    '业务方法': 2,
                    '检查点方法': 3,
                    '场景方法': 4
                };
                const orderA = order[a[0]] || 999;
                const orderB = order[b[0]] || 999;
                return orderA - orderB;
            });

        // 创建每个分类的方法列表
        for (const [categoryKey, methods] of sortedCategories) {
            // 如果分类下没有方法，跳过
            if (!methods || Object.keys(methods).length === 0) {
                continue;
            }

            // 创建分类标题
            const categoryHeader = doc.createElement('div');
            categoryHeader.className = 'category-header';
            categoryHeader.textContent = categoryKey;
            methodList.appendChild(categoryHeader);

            // 创建方法列表容器
            const methodsContainer = doc.createElement('div');
            methodsContainer.className = 'methods-container';

            // 编辑模式时仅展示当前方法
            let entries: Record<string, any> = methods;
            if (this.editMode) {
                if (!methods[this.editedMethodName]) { continue; }
                entries = { [this.editedMethodName]: methods[this.editedMethodName] };
            }

            // 添加方法项
            for (const [methodName, methodInfo] of Object.entries(entries)) {
                const methodItem = doc.createElement('div');
                methodItem.className = 'method-item';
                methodItem.setAttribute('data-method', methodName);
                //methodItem.textContent = methodName;
                //修改当前插入公共方法左侧区域显示方法名称为方法概述
                methodItem.textContent = methodInfo.overview;
                if (methodInfo.overview) { methodItem.title = methodInfo.overview; }
                if (this.editMode) {
                    methodItem.classList.add('disabled');
                }
                methodsContainer.appendChild(methodItem);
            }

            methodList.appendChild(methodsContainer);
        }

        container.appendChild(methodList);
    }

    /**
     * 创建方法详情区域
     */
    private createMethodDetails(doc: Document, container: HTMLElement): void {
        // 创建方法详情容器
        const methodDetails = doc.createElement('div');
        methodDetails.className = 'method-details';

        // 创建占位内容
        const contentPlaceholder = doc.createElement('div');
        contentPlaceholder.className = 'content-placeholder';
        contentPlaceholder.textContent = '请从左侧选择一个方法';
        container.appendChild(contentPlaceholder);

        // 创建方法标题
        const methodTitle = doc.createElement('h2');
        methodTitle.className = 'method-title';
        methodDetails.appendChild(methodTitle);

        // 创建方法描述
        const descriptionBox = doc.createElement('div');
        descriptionBox.className = 'description-box';
        const descriptionLabel = doc.createElement('span');
        descriptionLabel.className = 'label';
        descriptionLabel.textContent = '描述\uff1a';
        const descriptionText = doc.createElement('span');
        descriptionBox.appendChild(descriptionLabel);
        descriptionBox.appendChild(descriptionText);
        methodDetails.appendChild(descriptionBox);

        // 创建参数区域
        const parametersSection = doc.createElement('div');
        parametersSection.className = 'parameters-section';
        const parametersTitle = doc.createElement('h3');
        parametersTitle.textContent = '参数\uff1a';
        parametersSection.appendChild(parametersTitle);

        // 创建参数表单
        const parametersForm = doc.createElement('form');
        parametersForm.className = 'parameters-form';
        parametersSection.appendChild(parametersForm);

        // 插入测试用例上下文隐藏字段
        const ctx = this.testCaseContext;
        if (ctx) {
            if (ctx.TestCaseID) {
                const hidId = doc.createElement('input');
                hidId.type = 'hidden'; hidId.name = 'TestCaseID'; hidId.value = String(ctx.TestCaseID);
                parametersForm.appendChild(hidId);
            }
            if (ctx.steps) {
                const hidSteps = doc.createElement('input');
                hidSteps.type = 'hidden'; hidSteps.name = 'TestCaseSteps'; hidSteps.value = JSON.stringify(ctx.steps);
                parametersForm.appendChild(hidSteps);
            }
        }

        methodDetails.appendChild(parametersSection);

        // 创建示例区域
        const examplesSection = doc.createElement('div');
        examplesSection.className = 'examples-section';
        const examplesTitle = doc.createElement('h3');
        examplesTitle.textContent = '示例\uff1a';
        examplesSection.appendChild(examplesTitle);
        const examplesContent = doc.createElement('pre');
        examplesContent.className = 'examples-content';
        examplesSection.appendChild(examplesContent);
        methodDetails.appendChild(examplesSection);

        container.appendChild(methodDetails);
    }

    /**
     * 创建底部工具栏
     */
    private createFooter(doc: Document, container: HTMLElement): void {
        const footer = doc.createElement('div');
        footer.className = 'footer';

        const cancelButton = doc.createElement('button');
        cancelButton.className = 'footer-button cancel-button';
        cancelButton.textContent = '取消';
        footer.appendChild(cancelButton);

        // 新增"连续插入"复选框
        const continuousLabel = doc.createElement('label');
        continuousLabel.className = 'footer-checkbox-label';
        const continuousCheckbox = doc.createElement('input');
        continuousCheckbox.type = 'checkbox';
        continuousCheckbox.id = 'continuousInsert';
        continuousCheckbox.className = 'form-checkbox';
        continuousLabel.appendChild(continuousCheckbox);
        continuousLabel.appendChild(doc.createTextNode('连续插入'));
        footer.appendChild(continuousLabel);

        const insertButton = doc.createElement('button');
        insertButton.className = 'footer-button insert-button';
        insertButton.textContent = this.editMode ? '编辑方法' : '插入方法';
        footer.appendChild(insertButton);

        container.appendChild(footer);
    }

    /**
     * 新增：提供获取选中节点的接口
     */
    public getSelectedSection(): string {
        return this.selectedSection;
    }
}
