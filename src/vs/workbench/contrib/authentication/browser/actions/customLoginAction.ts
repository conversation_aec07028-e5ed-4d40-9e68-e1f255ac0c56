import { localize } from '../../../../../nls.js';
import { Action2 } from '../../../../../platform/actions/common/actions.js';
import { IDialogService } from '../../../../../platform/dialogs/common/dialogs.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICustomAuthenticationService } from '../../../../../platform/authentication/common/customAuthenticationService.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';


export class CustomLoginAction extends Action2 {
    constructor() {
        super({
            id: 'workbench.action.customLogin',
            title: localize('customLogin', "<PERSON><PERSON><PERSON>登录"),
            f1: false
        });
    }

    override async run(accessor: ServicesAccessor): Promise<void> {
        const dialogService = accessor.get(IDialogService);
        const notificationService = accessor.get(INotificationService);
        const logService = accessor.get(ILogService);
        const customAuthenticationService = accessor.get(ICustomAuthenticationService);
        const configurationService = accessor.get(IConfigurationService);

        // 检查是否启用调试模式
        const debugMode = configurationService.getValue('auth.customLogin.debugMode') as boolean;

        if (debugMode) {
            // 调试模式下的简化登录
            const result = await dialogService.input({
                message: localize('customLoginDebugMode', "调试模式登录"),
                primaryButton: localize('debugLoginButton', '调试登录'),
                cancelButton: localize('cancel', '取消'),
                inputs: [
                    { placeholder: localize('debugUsername', "调试用户名（可选）"), value: 'debug_user' }
                ],
                detail: localize('debugLoginDetail', "调试模式已启用，将跳过统一认证。")
            });

            if (!result.confirmed || !result.values) {
                return;
            }

            const [username] = result.values;
            const debugUsername = username || 'debug_user';

            try {
                const userInfo = await customAuthenticationService.login(debugUsername, 'debug_password');
                logService.info(`调试模式登录成功：${userInfo}`);
                notificationService.info(`${userInfo?.name}, 调试模式登录成功!`);
            } catch (error) {
                logService.error('调试模式登录失败:', error);
                await dialogService.error(localize('debugLoginError', "调试模式登录失败: {0}", error.message));
            }
            return;
        }

        // 正常登录流程
        const result = await dialogService.input({
            message: localize('customLoginAuthRequired', "Kylin账户登录"),
            primaryButton: localize('customLoginButton', '统一认证登录'),
            cancelButton: localize('cancel', '取消'),
            inputs:
                [
                    { placeholder: localize('username', "用户名"), value: '' },
                    { placeholder: localize('password', "密码"), type: 'password', value: '' }
                ],
            detail: localize('customLoginDetail', "请输入统一认证的用户名和密码。")
        });

        if (!result.confirmed || !result.values) {
            return;
        }

        const [username, password] = result.values;
        if (!username || !password) {
            notificationService.warn('用户名或密码不能为空');
            return;
        }
        const statusDisposable = notificationService.status('正在登录...', { hideAfter: 30000 });
        try {
            const userInfo = await customAuthenticationService.login(username, password);
            logService.info(`Kylin登录成功：${userInfo}`);

            statusDisposable.dispose();
            notificationService.info(`${userInfo?.name}, 欢迎登录!`);
        } catch (error) {
            logService.error('Kylin登录失败:', error);

            statusDisposable.dispose();
            await dialogService.error(localize('customLoginError', "Kylin登录失败: {0}", error.message));
        }
    }
}
