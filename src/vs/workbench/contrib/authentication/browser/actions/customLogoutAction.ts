import { localize } from '../../../../../nls.js';
import { Action2 } from '../../../../../platform/actions/common/actions.js';
import { IDialogService } from '../../../../../platform/dialogs/common/dialogs.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ICustomAuthenticationService } from '../../../../../platform/authentication/common/customAuthenticationService.js';


export class CustomLogoutAction extends Action2 {
	constructor() {
		super({
			id: 'workbench.action.customLogout',
			title: localize('customLogout', "退出登录"),
			f1: false
		});
	}

	override async run(accessor: ServicesAccessor): Promise<void> {
		const dialogService = accessor.get(IDialogService);
		const notificationService = accessor.get(INotificationService);
		const logService = accessor.get(ILogService);
		const customAuthenticationService = accessor.get(ICustomAuthenticationService);

		// 显示登录对话框
		const result = await dialogService.confirm({
			type: 'warning',
			message: localize('customLogoutAuthRequired', "退出登录"),
			primaryButton: localize('yes', '退出登录'),
			cancelButton: localize('cancel', '取消'),
			detail: localize('customLogoutDetail', "您确定要退出登录吗")
		});

		if (!result.confirmed) {
			return;
		}

		try {
			customAuthenticationService.logout();
			logService.info('Kylin退出登录成功');
			notificationService.info('您已退出Kylin登录');
		} catch (error) {
			logService.error('Kylin退出登录失败:', error);
			await dialogService.error(localize('customLogoutError', "Kylin退出登录失败: {0}", error.message));
		}
	}
}

