import { localize } from '../../../../../nls.js';
import { Action2 } from '../../../../../platform/actions/common/actions.js';
import { IDialogService } from '../../../../../platform/dialogs/common/dialogs.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICustomAuthenticationService } from '../../../../../platform/authentication/common/customAuthenticationService.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';

export class DebugLoginAction extends Action2 {
    constructor() {
        super({
            id: 'workbench.action.debugLogin',
            title: {
                value: localize('debugLogin', "调试模式登录"),
                original: 'Debug Login'
            },
            f1: true,
            category: {
                value: localize('authentication', "认证"),
                original: 'Authentication'
            }
        });
    }

    override async run(accessor: ServicesAccessor): Promise<void> {
        const dialogService = accessor.get(IDialogService);
        const notificationService = accessor.get(INotificationService);
        const logService = accessor.get(ILogService);
        const customAuthenticationService = accessor.get(ICustomAuthenticationService);
        const configurationService = accessor.get(IConfigurationService);

        // 检查当前是否已经登录
        if (customAuthenticationService.isLoggedIn()) {
            const currentUser = customAuthenticationService.getName();
            notificationService.info(localize('alreadyLoggedIn', "已登录用户: {0}", currentUser));
            return;
        }

        // 询问是否启用调试模式
        const enableDebugResult = await dialogService.confirm({
            message: localize('enableDebugModeTitle', "启用调试模式"),
            detail: localize('enableDebugModeDetail', "是否启用调试模式？启用后将跳过统一认证登录，直接使用模拟用户信息。"),
            primaryButton: localize('enableDebugMode', "启用调试模式"),
            cancelButton: localize('cancel', "取消")
        });

        if (!enableDebugResult.confirmed) {
            return;
        }

        // 启用调试模式
        await configurationService.updateValue('auth.customLogin.debugMode', true);
        logService.info('调试模式已启用');

        // 获取调试用户名
        const result = await dialogService.input({
            message: localize('debugLoginTitle', "调试模式登录"),
            primaryButton: localize('debugLoginButton', '调试登录'),
            cancelButton: localize('cancel', '取消'),
            inputs: [
                { placeholder: localize('debugUsername', "调试用户名"), value: 'debug_user' }
            ],
            detail: localize('debugLoginDetail', "调试模式已启用，请输入调试用户名。")
        });

        if (!result.confirmed || !result.values) {
            return;
        }

        const [username] = result.values;
        const debugUsername = username || 'debug_user';

        try {
            const userInfo = await customAuthenticationService.login(debugUsername, 'debug_password');
            logService.info(`调试模式登录成功：${JSON.stringify(userInfo)}`);
            notificationService.info(localize('debugLoginSuccess', "{0}, 调试模式登录成功!", userInfo?.name));
        } catch (error) {
            logService.error('调试模式登录失败:', error);
            await dialogService.error(localize('debugLoginError', "调试模式登录失败: {0}", error.message));
        }
    }
}

export class DisableDebugModeAction extends Action2 {
    constructor() {
        super({
            id: 'workbench.action.disableDebugMode',
            title: {
                value: localize('disableDebugMode', "禁用调试模式"),
                original: 'Disable Debug Mode'
            },
            f1: true,
            category: {
                value: localize('authentication', "认证"),
                original: 'Authentication'
            }
        });
    }

    override async run(accessor: ServicesAccessor): Promise<void> {
        const dialogService = accessor.get(IDialogService);
        const notificationService = accessor.get(INotificationService);
        const logService = accessor.get(ILogService);
        const configurationService = accessor.get(IConfigurationService);
        const customAuthenticationService = accessor.get(ICustomAuthenticationService);

        const result = await dialogService.confirm({
            message: localize('disableDebugModeTitle', "禁用调试模式"),
            detail: localize('disableDebugModeDetail', "是否禁用调试模式？禁用后需要重新进行统一认证登录。"),
            primaryButton: localize('disableDebugMode', "禁用调试模式"),
            cancelButton: localize('cancel', "取消")
        });

        if (!result.confirmed) {
            return;
        }

        // 禁用调试模式
        await configurationService.updateValue('auth.customLogin.debugMode', false);

        // 登出当前用户
        customAuthenticationService.logout();

        logService.info('调试模式已禁用，用户已登出');
        notificationService.info(localize('debugModeDisabled', "调试模式已禁用，请重新登录"));
    }
}
