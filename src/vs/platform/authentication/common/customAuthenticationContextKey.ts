import * as nls from '../../../nls.js';
import { RawContextKey } from '../../../platform/contextkey/common/contextkey.js';

export namespace CustomAuthenticationContextKeys {

	// 统一认证登录状态
	export const customLoginStatus = new RawContextKey<boolean>('customLoginStatus', false, nls.localize('customLoginStatus', "用户是否登录统一认证"));

	// 调试模式状态
	export const debugModeEnabled = new RawContextKey<boolean>('debugModeEnabled', false, nls.localize('debugModeEnabled', "是否启用调试模式"));
}
