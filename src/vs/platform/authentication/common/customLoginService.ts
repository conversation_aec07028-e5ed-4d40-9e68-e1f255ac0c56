import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';

export interface ICustomLoginOptions {
	type?: string;
	url: string;
	data: string;
	headers?: {
		[key: string]: string;
	};
}

export interface ICustomLoginInfo {
	name: string;
	username: string;
	token: string;
}

export const ICustomLoginService = createDecorator<ICustomLoginService>('customLoginService');

export interface ICustomLoginService {
	readonly _serviceBrand: undefined;

	login(options: ICustomLoginOptions): Promise<ICustomLoginInfo | undefined>;
}
