import { Emitter, Event } from '../../../base/common/event.js';
import { Disposable } from '../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../platform/storage/common/storage.js';
import { IConfigurationService } from '../../../platform/configuration/common/configuration.js';
import { IMainProcessService } from '../../../platform/ipc/common/mainProcessService.js';
import { ProxyChannel } from '../../../base/parts/ipc/common/ipc.js';
import { IContextKey, IContextKeyService } from '../../../platform/contextkey/common/contextkey.js';
import { ICustomLoginOptions, ICustomLoginService } from './customLoginService.js';
import { CustomAuthenticationContextKeys } from './customAuthenticationContextKey.js';


export interface ICustomUserInfo {
	name: string;
	username: string;
	token: string;
	loginTime?: number;
}


export const ICustomAuthenticationService = createDecorator<ICustomAuthenticationService>('customAuthenticationService');
export interface ICustomAuthenticationService {
	readonly _serviceBrand: undefined;

	getUserInfo(): ICustomUserInfo | undefined;
	getName(): string | undefined;
	getUsername(): string | undefined;
	login(username: string, password: string): Promise<ICustomUserInfo | undefined>;
	isLoggedIn(): boolean;
	logout(): void;

	readonly onDidLoginAuthenticationProvider: Event<ICustomUserInfo>;
	readonly onDidLogoutAuthenticationProvider: Event<void>;
	readonly onDidAuthenticationChangedProvider: Event<void>;
}

export class CustomAuthenticationService extends Disposable implements ICustomAuthenticationService {
	readonly _serviceBrand: undefined;

	private _onDidLoginAuthenticationProvider: Emitter<ICustomUserInfo> = this._register(new Emitter<ICustomUserInfo>());
	readonly onDidLoginAuthenticationProvider: Event<ICustomUserInfo> = this._onDidLoginAuthenticationProvider.event;

	private _onDidLogoutAuthenticationProvider: Emitter<void> = this._register(new Emitter<void>());
	readonly onDidLogoutAuthenticationProvider: Event<void> = this._onDidLogoutAuthenticationProvider.event;

	private _onDidAuthenticationChangedProvider: Emitter<void> = this._register(new Emitter<void>());
	readonly onDidAuthenticationChangedProvider: Event<void> = this._onDidAuthenticationChangedProvider.event;

	private readonly _customLoginStatus: IContextKey<boolean>;
	private readonly _debugModeEnabled: IContextKey<boolean>;

	private STORAGE_KEY_USER_INFO = 'customLogin.userInfo';

	constructor(
		@IMainProcessService private readonly _mainProcessService: IMainProcessService,
		@IStorageService private readonly _storageService: IStorageService,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@ILogService private readonly _logService: ILogService,
		@IContextKeyService private readonly _contextKeyService: IContextKeyService
	) {
		super();
		this._customLoginStatus = CustomAuthenticationContextKeys.customLoginStatus.bindTo(this._contextKeyService);
		this._debugModeEnabled = CustomAuthenticationContextKeys.debugModeEnabled.bindTo(this._contextKeyService);
		this.initCustomLoginStatus();
		this.initDebugModeStatus();
	}

	initCustomLoginStatus() {
		this._customLoginStatus.set(this.isLoggedIn());
	}

	initDebugModeStatus() {
		const debugMode = this._configurationService.getValue('auth.customLogin.debugMode') as boolean;
		this._debugModeEnabled.set(!!debugMode);
	}

	async login(username: string, password: string): Promise<ICustomUserInfo | undefined> {
		// 检查是否启用调试模式
		const debugMode = this._configurationService.getValue('auth.customLogin.debugMode') as boolean;
		this.initDebugModeStatus();

		if (debugMode) {
			this._logService.info('调试模式已启用，跳过统一认证登录');
			// 在调试模式下创建模拟用户信息
			const userInfo = {
				name: `调试用户 (${username})`,
				username: username || 'debug_user',
				token: 'debug_token_' + Date.now(),
				loginTime: Date.now(),
			};
			this._storageService.store(this.STORAGE_KEY_USER_INFO, JSON.stringify(userInfo), StorageScope.APPLICATION, StorageTarget.MACHINE);
			this.initCustomLoginStatus();
			this._onDidLoginAuthenticationProvider.fire(userInfo);
			this._onDidAuthenticationChangedProvider.fire();
			return userInfo;
		}

		const url = this._configurationService.getValue('auth.customLogin.loginUrl') as string;
		if (!url) {
			throw new Error('登录失败：未配置统一认证接口地址');
		}

		const data = JSON.stringify({ password, username, domain: 'kydc' });
		const requestOptions: ICustomLoginOptions = {
			type: 'POST',
			url,
			data,
			headers: {
				'Content-Type': 'application/json'
			},
		};
		const _options = JSON.stringify(requestOptions);
		this._logService.info(`Kylin登录信息: ${_options}`);

		const _customLoginService = ProxyChannel.toService<ICustomLoginService>(this._mainProcessService.getChannel('customLoginMainService'));
		const loginInfo = await _customLoginService.login(requestOptions);

		if (!loginInfo) {
			throw new Error('登录失败：未获取到登录信息');
		}

		this._logService.info(`Kylin登录结果: ${loginInfo}`);
		console.log(loginInfo);
		const userInfo = {
			name: loginInfo.name,
			username: loginInfo.username || username,
			token: loginInfo.token,
			loginTime: Date.now(),
		};
		this._storageService.store(this.STORAGE_KEY_USER_INFO, JSON.stringify(userInfo), StorageScope.APPLICATION, StorageTarget.MACHINE);
		this.initCustomLoginStatus();
		this._onDidLoginAuthenticationProvider.fire(userInfo);
		this._onDidAuthenticationChangedProvider.fire();
		return userInfo;
	}

	getUserInfo(): ICustomUserInfo | undefined {
		const stored = this._storageService.get(this.STORAGE_KEY_USER_INFO, StorageScope.APPLICATION);
		if (stored) {
			try {
				return JSON.parse(stored);
			} catch {
				return undefined;
			}
		}
		return undefined;
	}

	getName(): string | undefined {
		const userInfo = this.getUserInfo();
		return userInfo?.name;
	}
	getUsername(): string | undefined {
		const userInfo = this.getUserInfo();
		return userInfo?.username;
	}

	isLoggedIn(): boolean {
		return !!this.getUserInfo();
	}

	logout(): void {
		this._storageService.remove(this.STORAGE_KEY_USER_INFO, StorageScope.APPLICATION);
		this.initCustomLoginStatus();
		this.initDebugModeStatus();
		this._onDidLogoutAuthenticationProvider.fire();
		this._onDidAuthenticationChangedProvider.fire();
	}
}


registerSingleton(ICustomAuthenticationService, CustomAuthenticationService, InstantiationType.Delayed);
