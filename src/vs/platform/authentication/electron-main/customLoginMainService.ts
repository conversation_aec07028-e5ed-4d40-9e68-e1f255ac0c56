import { Event } from '../../../base/common/event.js';
import { Disposable, IDisposable } from '../../../base/common/lifecycle.js';
import { streamToBuffer } from '../../../base/common/buffer.js';
import { CancellationToken } from '../../../base/common/cancellation.js';
import { IServerChannel } from '../../../base/parts/ipc/common/ipc.js';
import { Server as ElectronIPCServer } from '../../../base/parts/ipc/electron-main/ipc.electron.js';
import { IRequestService } from '../../../platform/request/common/request.js';
import { IInstantiationService } from '../../../platform/instantiation/common/instantiation.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { ICustomLoginService, ICustomLoginInfo, ICustomLoginOptions } from '../common/customLoginService.js';


export class CustomLoginMainService extends Disposable implements ICustomLoginService {
	readonly _serviceBrand: undefined;

	constructor(
		@IRequestService private readonly _requestService: IRequestService,
		@ILogService private readonly _logService: ILogService,
	) {
		super();
	}

	async login(options: ICustomLoginOptions): Promise<ICustomLoginInfo | undefined> {
		let responseStr = '';
		try {

			const optionsStr = JSON.stringify(options);
			this._logService.info(`[CustomLoginMainService] Kylin登录信息: ${optionsStr}`);

			const defaultHeaders = { 'Content-Type': 'application/json' };
			const response = await this._requestService.request({
				type: options?.type ?? 'POST',
				url: options.url,
				data: options.data,
				headers: options?.headers ?? defaultHeaders,
			}, CancellationToken.None);

			if (response.res.statusCode !== 200) {
				throw new Error(`[CustomLoginMainService] 登录失败: ${response.res.statusCode}`);
			}

			const buffer = await streamToBuffer(response.stream);
			responseStr = buffer.toString();
			const loginInfo = JSON.parse(responseStr);

			this._logService.info(`[CustomLoginMainService] 登录结果: ${responseStr}`);
			const userInfo = loginInfo?.data;
			if (!userInfo || !userInfo.name || !userInfo.username) {
				throw new Error(`[CustomLoginMainService] 登录失败: 未获取到登录信息`);
			}
			if (!userInfo.name) {
				throw new Error(`[CustomLoginMainService] 登录失败: 未获取到用户信息`);
			}
			const result: ICustomLoginInfo = {
				name: userInfo.name,
				username: userInfo.username,
				token: userInfo.token,
			};
			return result;
		} catch (err) {
			err.message += ':\n' + responseStr;
			throw err;
		}
	}
}


/** 服务通道 */
class CustomLoginChannel implements IServerChannel {
	constructor(private readonly service: CustomLoginMainService) { }

	public listen(_: unknown, event: string): Event<any> {
		throw new Error(`Event not supported: ${event}`);
	}
	call(_: unknown, command: string, arg?: any): Promise<any> {
		if (command === 'login') {
			const _arg = Array.isArray(arg) ? arg[0] : arg;
			return this.service.login(_arg);
		}
		return Promise.reject(new Error(`Unknown command: ${command}`));
	}
}


export function initCustomLoginMainService(instantiationService: IInstantiationService, logService: ILogService): IDisposable {
	logService.info('初始化登录主进程服务');
	const service = instantiationService.createInstance(CustomLoginMainService);
	const channel = new CustomLoginChannel(service);
	const server = new ElectronIPCServer();
	server.registerChannel('customLoginMainService', channel);
	logService.info('CustomLogin通道已注册');
	return { dispose: () => service.dispose() };
}
