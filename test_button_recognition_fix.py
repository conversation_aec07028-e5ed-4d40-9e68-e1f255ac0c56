#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮识别修复的脚本
"""

import sys
import os

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from UNI import UNI
except ImportError as e:
    print(f"❌ 无法导入UNI模块: {e}")
    sys.exit(1)

def test_button_recognition():
    """测试按钮识别修复"""
    print("🔧 测试按钮识别修复")
    print("=" * 60)
    
    # 创建UNI实例
    uni = UNI()
    
    # 测试坐标 - 这些坐标应该在按钮区域内
    test_coordinates = [
        (869, 458),  # 原始问题坐标
        (860, 450),  # 按钮中心附近
        (850, 460),  # 按钮边缘
    ]
    
    for i, (x, y) in enumerate(test_coordinates, 1):
        print(f"\n🧪 测试 {i}: 坐标 ({x}, {y})")
        print("-" * 40)
        
        try:
            data, text_info = uni.kdk_getElement_Uni(x, y)
            
            if data and "error" not in data:
                print(f"✅ 识别成功:")
                print(f"   控件名称: {data.get('Name', 'N/A')}")
                print(f"   控件角色: {data.get('Rolename', 'N/A')}")
                print(f"   控件坐标: {data.get('Coords', 'N/A')}")
                print(f"   控件动作: {data.get('Actions', [])}")
                
                # 检查是否是按钮类型
                role = data.get('Rolename', '').lower()
                actions = data.get('Actions', [])
                
                if 'button' in role:
                    print(f"   🎯 成功识别为按钮控件!")
                elif 'label' in role:
                    print(f"   ⚠️  识别为标签控件，可能需要进一步优化")
                else:
                    print(f"   ❓ 识别为其他类型控件: {role}")
                    
                # 检查是否有点击动作
                if isinstance(actions, list) and 'Press' in actions:
                    print(f"   ✅ 控件支持点击动作")
                elif isinstance(actions, list) and len(actions) > 0:
                    print(f"   ⚠️  控件支持的动作: {actions}")
                else:
                    print(f"   ❌ 控件不支持点击动作")
                    
            else:
                print(f"❌ 识别失败:")
                print(f"   错误信息: {data.get('error', '未知错误') if data else '无返回数据'}")
                
        except Exception as e:
            print(f"❌ 识别异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔧 测试完成")

def test_post_process_logic():
    """测试后处理逻辑"""
    print("\n🧪 测试后处理逻辑")
    print("=" * 60)
    
    # 这里可以添加更具体的后处理逻辑测试
    # 由于需要实际的控件对象，这里只做概念性测试
    
    print("✅ 后处理逻辑已添加到UNI.py中")
    print("   - 当识别到按钮内部的标签时，会返回按钮本身")
    print("   - 这样用户点击按钮时会得到可点击的按钮控件")
    print("   - 而不是不可点击的标签控件")

if __name__ == "__main__":
    print("🚀 按钮识别修复测试工具")
    print("📍 测试目标：确保按钮点击时返回按钮而不是标签")
    print()
    
    # 测试按钮识别
    test_button_recognition()
    
    # 测试后处理逻辑
    test_post_process_logic()
    
    print()
    print("💡 修复说明:")
    print("1. 添加了 _post_process_button_label 方法")
    print("2. 当识别到标签控件时，检查是否有对应的按钮控件")
    print("3. 如果标签在按钮内部，返回按钮而不是标签")
    print("4. 这样确保用户点击按钮时得到可操作的控件")
    print()
    print("🔍 如果测试结果仍然不理想，可能需要:")
    print("1. 调整智能深度搜索的得分算法")
    print("2. 优化按钮和标签的关系判断逻辑")
    print("3. 检查具体的控件层级结构")
