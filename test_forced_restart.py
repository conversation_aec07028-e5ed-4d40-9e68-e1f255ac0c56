#!/usr/bin/env python3
"""
测试强制重启功能 - 控件识别超过3秒立即重启
"""

import time
import sys
import os
import threading
from unittest.mock import Mock, patch

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

def test_forced_restart_functionality():
    """测试强制重启功能"""
    print("🧪 测试控件识别超过3秒强制重启功能")
    print("=" * 60)
    
    try:
        from auto_recording_manager import HoverDetector, WidgetAnalyzer
        
        # 创建模拟的WidgetAnalyzer，模拟长时间阻塞
        class BlockingWidgetAnalyzer:
            def __init__(self, debug=False):
                self.debug = debug
                
            def analyze_widget_at_with_new_app_detection(self, x, y, interrupt_flag=None):
                """模拟长时间阻塞的控件识别（18秒）"""
                print(f"[MOCK] 🐌 开始模拟长时间阻塞的控件识别... 位置=({x}, {y})")
                print(f"[MOCK] 🐌 这将阻塞18秒，但应该在3秒后被强制重启中断")
                
                # 模拟18秒的长时间阻塞
                for i in range(18):
                    time.sleep(1)
                    print(f"[MOCK] 🐌 阻塞中... {i+1}/18秒", file=sys.stderr)
                
                print(f"[MOCK] ❌ 如果看到这条消息，说明强制重启没有生效！")
                return {
                    'Name': 'MockWidget',
                    'Rolename': 'button',
                    'ProcessName': 'test_app',
                    'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
                }, "模拟识别成功"
                
            def analyze_widget_at(self, x, y, interrupt_flag=None):
                """模拟长时间阻塞的控件识别（简化版）"""
                print(f"[MOCK] 🐌 开始模拟长时间阻塞的控件识别(简化版)... 位置=({x}, {y})")
                print(f"[MOCK] 🐌 这将阻塞18秒，但应该在3秒后被强制重启中断")
                
                # 模拟18秒的长时间阻塞
                for i in range(18):
                    time.sleep(1)
                    print(f"[MOCK] 🐌 阻塞中(简化版)... {i+1}/18秒", file=sys.stderr)
                
                print(f"[MOCK] ❌ 如果看到这条消息，说明强制重启没有生效！")
                return {
                    'Name': 'MockWidget',
                    'Rolename': 'button',
                    'ProcessName': 'test_app',
                    'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
                }, "模拟识别成功"
        
        # 创建HoverDetector实例
        blocking_analyzer = BlockingWidgetAnalyzer(debug=True)
        hover_detector = HoverDetector(blocking_analyzer, debug=True)
        
        # 模拟restart_self方法，避免真正重启
        restart_called = threading.Event()
        restart_time = {'time': 0}
        
        def mock_restart_self():
            restart_time['time'] = time.time()
            print("🔄 [MOCK] ✅ restart_self() 被调用！强制重启成功触发！")
            print("🔄 [MOCK] 在真实环境中，这里会立即重启进程")
            restart_called.set()
            
        hover_detector.restart_self = mock_restart_self
        
        # 模拟_force_clear_all_highlights方法
        def mock_clear_highlights():
            print("🧹 [MOCK] _force_clear_all_highlights() 被调用")
            
        hover_detector._force_clear_all_highlights = mock_clear_highlights
        
        print("\n📍 测试1: 悬停检测强制重启（3秒超时）")
        print("-" * 50)
        
        # 测试悬停超时回调
        test_start_time = time.time()
        print(f"⏰ 测试开始时间: {time.strftime('%H:%M:%S', time.localtime(test_start_time))}")
        print(f"🎯 预期：应该在3秒后触发强制重启")
        
        # 在新线程中调用悬停超时回调（避免阻塞测试）
        def run_hover_test():
            hover_detector._on_hover_timeout(400, 300)
            
        test_thread = threading.Thread(target=run_hover_test, daemon=True)
        test_thread.start()
        
        # 等待重启被触发（最多等待5秒）
        if restart_called.wait(timeout=5.0):
            restart_elapsed = restart_time['time'] - test_start_time
            print(f"✅ 测试成功：强制重启在 {restart_elapsed:.3f}秒 后触发")
            if 2.8 <= restart_elapsed <= 3.5:
                print(f"✅ 时间精度良好：重启时间在预期的3秒左右")
            else:
                print(f"⚠️ 时间精度偏差：预期3秒，实际{restart_elapsed:.3f}秒")
        else:
            print("❌ 测试失败：5秒内未触发强制重启")
            
        print("\n📍 测试2: 悬停录制强制重启（3秒超时）")
        print("-" * 50)
        
        # 重置标志
        restart_called.clear()
        restart_time['time'] = 0
        
        # 测试悬停录制超时回调
        test_start_time = time.time()
        print(f"⏰ 测试开始时间: {time.strftime('%H:%M:%S', time.localtime(test_start_time))}")
        print(f"🎯 预期：应该在3秒后触发强制重启")
        
        # 在新线程中调用悬停录制超时回调
        def run_hover_record_test():
            hover_detector._on_hover_record_timeout(500, 400)
            
        test_thread2 = threading.Thread(target=run_hover_record_test, daemon=True)
        test_thread2.start()
        
        # 等待重启被触发（最多等待5秒）
        if restart_called.wait(timeout=5.0):
            restart_elapsed = restart_time['time'] - test_start_time
            print(f"✅ 测试成功：强制重启在 {restart_elapsed:.3f}秒 后触发")
            if 2.8 <= restart_elapsed <= 3.5:
                print(f"✅ 时间精度良好：重启时间在预期的3秒左右")
            else:
                print(f"⚠️ 时间精度偏差：预期3秒，实际{restart_elapsed:.3f}秒")
        else:
            print("❌ 测试失败：5秒内未触发强制重启")
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保scripts/auto_recording_manager.py文件存在")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_timing_precision():
    """测试时间精度"""
    print("\n🧪 测试强制重启时间精度")
    print("=" * 60)
    
    print("📊 测试超时监控线程的精度...")
    
    start_times = []
    end_times = []
    
    def timeout_test(timeout_seconds):
        start_time = time.time()
        start_times.append(start_time)
        
        def monitor():
            time.sleep(timeout_seconds)
            end_time = time.time()
            end_times.append(end_time)
            elapsed = end_time - start_time
            print(f"⏱️ 超时监控: 设定{timeout_seconds}秒，实际{elapsed:.3f}秒")
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
        thread.join()
    
    # 测试不同的超时时间
    for timeout in [1.0, 2.0, 3.0]:
        timeout_test(timeout)
    
    print("✅ 时间精度测试完成")

if __name__ == "__main__":
    print("🚀 控件识别强制重启功能测试")
    print("=" * 80)
    
    # 测试强制重启功能
    test_forced_restart_functionality()
    
    # 测试时间精度
    test_timing_precision()
    
    print("\n✅ 测试完成")
    print("\n📝 功能说明:")
    print("1. 🚨 强制重启机制：控件识别超过3秒立即重启进程")
    print("2. ⏱️ 精确计时：使用独立的监控线程进行超时检测")
    print("3. 🔄 立即响应：不等待识别完成，直接中断并重启")
    print("4. 🧹 状态清理：重启前自动清理高亮状态")
    print("5. 🎯 双重保护：悬停检测和悬停录制都有强制重启保护")
    print("\n⚠️ 注意：在真实环境中，重启会立即终止当前进程并启动新进程")
