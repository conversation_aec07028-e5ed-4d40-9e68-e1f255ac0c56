#!/usr/bin/env python3
"""
测试控件识别超时自动重启功能
"""

import time
import sys
import os
import threading
from unittest.mock import Mock, patch

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

def test_timeout_restart_functionality():
    """测试超时重启功能"""
    print("🧪 测试控件识别超时自动重启功能")
    print("=" * 60)

    try:
        from auto_recording_manager import HoverDetector, WidgetAnalyzer

        # 创建模拟的WidgetAnalyzer，模拟超时情况
        class SlowWidgetAnalyzer:
            def __init__(self, debug=False):
                self.debug = debug

            def analyze_widget_at_with_new_app_detection(self, x, y, interrupt_flag=None):
                """模拟耗时超过3秒的控件识别"""
                print(f"[MOCK] 开始模拟慢速控件识别... 位置=({x}, {y})")
                print(f"[MOCK] 这将耗时18秒来模拟真实的长时间识别...")

                # 模拟耗时18秒的控件识别（模拟真实的长时间阻塞）
                time.sleep(18.0)

                print(f"[MOCK] 模拟控件识别完成（实际耗时18秒）")
                return {
                    'Name': 'MockWidget',
                    'Rolename': 'button',
                    'ProcessName': 'test_app',
                    'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
                }, "模拟识别成功"

            def analyze_widget_at(self, x, y, interrupt_flag=None):
                """模拟耗时超过3秒的控件识别"""
                print(f"[MOCK] 开始模拟慢速控件识别(简单版)... 位置=({x}, {y})")
                print(f"[MOCK] 这将耗时18秒来模拟真实的长时间识别...")

                # 模拟耗时18秒的控件识别
                time.sleep(18.0)

                print(f"[MOCK] 模拟控件识别完成(简单版)（实际耗时18秒）")
                return {
                    'Name': 'MockWidget',
                    'Rolename': 'button',
                    'ProcessName': 'test_app',
                    'Coords': {'x': x, 'y': y, 'width': 100, 'height': 30}
                }, "模拟识别成功"

        # 创建HoverDetector实例
        slow_analyzer = SlowWidgetAnalyzer(debug=True)
        hover_detector = HoverDetector(slow_analyzer, debug=True)

        # 模拟restart_self方法，避免真正重启
        original_restart = hover_detector.restart_self
        restart_called = threading.Event()

        def mock_restart_self():
            print("🔄 [MOCK] restart_self() 被调用！")
            print("🔄 [MOCK] 在真实环境中，这里会重启进程")
            restart_called.set()

        hover_detector.restart_self = mock_restart_self

        # 模拟_force_clear_all_highlights方法
        def mock_clear_highlights():
            print("🧹 [MOCK] _force_clear_all_highlights() 被调用")

        hover_detector._force_clear_all_highlights = mock_clear_highlights

        print("\n📍 测试1: 悬停超时触发控件识别")
        print("-" * 40)

        # 测试悬停超时回调
        start_time = time.time()
        print(f"⏰ 开始时间: {time.strftime('%H:%M:%S', time.localtime(start_time))}")

        # 调用悬停超时回调（这会触发控件识别）
        hover_detector._on_hover_timeout(400, 300)

        end_time = time.time()
        total_time = end_time - start_time

        print(f"⏰ 结束时间: {time.strftime('%H:%M:%S', time.localtime(end_time))}")
        print(f"⏱️ 总耗时: {total_time:.3f}秒")

        # 检查是否调用了重启
        if restart_called.wait(timeout=1.0):
            print("✅ 测试成功：超时检测正常工作，restart_self() 被调用")
        else:
            print("❌ 测试失败：超时检测未触发重启")

        print("\n📍 测试2: 悬停录制超时触发控件识别")
        print("-" * 40)

        # 重置标志
        restart_called.clear()

        # 测试悬停录制超时回调
        start_time = time.time()
        print(f"⏰ 开始时间: {time.strftime('%H:%M:%S', time.localtime(start_time))}")

        # 调用悬停录制超时回调
        hover_detector._on_hover_record_timeout(500, 400)

        end_time = time.time()
        total_time = end_time - start_time

        print(f"⏰ 结束时间: {time.strftime('%H:%M:%S', time.localtime(end_time))}")
        print(f"⏱️ 总耗时: {total_time:.3f}秒")

        # 检查是否调用了重启
        if restart_called.wait(timeout=1.0):
            print("✅ 测试成功：悬停录制超时检测正常工作，restart_self() 被调用")
        else:
            print("❌ 测试失败：悬停录制超时检测未触发重启")

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保scripts/auto_recording_manager.py文件存在")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_restart_self_method():
    """测试restart_self方法本身"""
    print("\n🧪 测试restart_self方法")
    print("=" * 60)

    try:
        from auto_recording_manager import HoverDetector

        # 创建HoverDetector实例
        hover_detector = HoverDetector(debug=True)

        print("📋 当前进程信息:")
        print(f"   Python路径: {sys.executable}")
        print(f"   脚本参数: {sys.argv}")
        print(f"   进程ID: {os.getpid()}")

        print("\n⚠️  注意：restart_self()会重启进程，这里只显示方法逻辑")
        print("如果要测试真正的重启，请手动调用 hover_detector.restart_self()")

    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("🚀 控件识别超时自动重启功能测试")
    print("=" * 80)

    # 测试超时重启功能
    test_timeout_restart_functionality()

    # 测试restart_self方法
    test_restart_self_method()

    print("\n✅ 测试完成")
    print("\n📝 功能说明:")
    print("1. 🔧 修复了异步超时检测问题：现在能正确测量真实的控件识别时间")
    print("2. ⏱️ 解决了'实际18秒但显示3秒'的问题：使用真实超时检测方法")
    print("3. 🚨 当控件识别耗时超过3秒时，会自动调用restart_self()重启进程")
    print("4. 🧹 重启前会清理高亮状态和其他资源")
    print("5. 🎯 重启功能适用于悬停检测和悬停录制两种场景")
    print("6. 🛡️ 这个机制可以解决AT-SPI状态异常或系统性能问题导致的识别阻塞")
    print("\n🔍 技术细节:")
    print("- 使用ThreadPoolExecutor在后台执行控件识别")
    print("- 通过共享变量记录真实的识别开始和结束时间")
    print("- 区分'等待超时时间'和'实际识别耗时'")
    print("- 即使任务超时，也能获取到真实的执行时间")
