#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同场景下的下拉菜单项识别
"""

import sys
import os
import time

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from UNI import UNI
except ImportError as e:
    print(f"❌ 无法导入UNI模块: {e}")
    sys.exit(1)

def test_dropdown_recognition_multiple_times(x, y, test_count=5):
    """多次测试下拉菜单项识别"""
    print(f"🔄 多次测试坐标({x}, {y})的识别结果")
    print("=" * 60)
    
    results = []
    
    for i in range(test_count):
        print(f"\n🧪 测试 {i+1}/{test_count}")
        print("-" * 30)
        
        uni = UNI()
        
        try:
            data, text_info = uni.kdk_getElement_Uni(x, y)
            
            if data and "error" not in data:
                result = {
                    'success': True,
                    'name': data.get('Name', 'N/A'),
                    'role': data.get('Rolename', 'N/A'),
                    'coords': data.get('Coords', {}),
                    'actions': data.get('Actions', [])
                }
                print(f"✅ 识别成功: {result['name']} ({result['role']})")
            else:
                result = {
                    'success': False,
                    'error': data.get('error', '未知错误') if data else '无返回数据'
                }
                print(f"❌ 识别失败: {result['error']}")
                
            results.append(result)
            
        except Exception as e:
            result = {
                'success': False,
                'error': f"异常: {e}"
            }
            results.append(result)
            print(f"❌ 识别异常: {e}")
        
        # 短暂延迟
        time.sleep(0.5)
    
    # 分析结果
    print(f"\n📊 测试结果分析:")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    failure_count = len(results) - success_count
    
    print(f"✅ 成功次数: {success_count}/{test_count}")
    print(f"❌ 失败次数: {failure_count}/{test_count}")
    print(f"📈 成功率: {success_count/test_count*100:.1f}%")
    
    if success_count > 0:
        # 分析成功的结果
        successful_results = [r for r in results if r['success']]
        names = [r['name'] for r in successful_results]
        roles = [r['role'] for r in successful_results]
        
        print(f"\n✅ 成功识别的控件:")
        for i, r in enumerate(successful_results, 1):
            print(f"   {i}. {r['name']} ({r['role']})")
        
        # 检查一致性
        unique_names = set(names)
        unique_roles = set(roles)
        
        if len(unique_names) == 1 and len(unique_roles) == 1:
            print(f"🎯 结果一致: 所有成功识别都是 {names[0]} ({roles[0]})")
        else:
            print(f"⚠️  结果不一致:")
            print(f"   识别到的名称: {unique_names}")
            print(f"   识别到的角色: {unique_roles}")
    
    if failure_count > 0:
        # 分析失败的原因
        failed_results = [r for r in results if not r['success']]
        errors = [r['error'] for r in failed_results]
        
        print(f"\n❌ 失败原因:")
        for i, error in enumerate(set(errors), 1):
            count = errors.count(error)
            print(f"   {i}. {error} (出现{count}次)")

def test_different_coordinates():
    """测试不同坐标的识别情况"""
    print("🎯 测试不同坐标的识别情况")
    print("=" * 60)
    
    # 测试坐标列表 - 围绕目标坐标的不同位置
    test_coords = [
        (431, 568),  # 原始坐标
        (430, 568),  # 左1px
        (432, 568),  # 右1px
        (431, 567),  # 上1px
        (431, 569),  # 下1px
        (425, 565),  # 左上角
        (437, 571),  # 右下角
        (408, 556),  # 选项左上角
        (720, 594),  # 选项右下角
        (564, 575),  # 选项中心
    ]
    
    uni = UNI()
    
    for i, (x, y) in enumerate(test_coords, 1):
        print(f"\n📍 测试坐标 {i}: ({x}, {y})")
        
        try:
            data, text_info = uni.kdk_getElement_Uni(x, y)
            
            if data and "error" not in data:
                name = data.get('Name', 'N/A')
                role = data.get('Rolename', 'N/A')
                coords = data.get('Coords', {})
                print(f"   ✅ {name} ({role}) - {coords}")
            else:
                error = data.get('error', '未知错误') if data else '无返回数据'
                print(f"   ❌ {error}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

if __name__ == "__main__":
    print("🚀 下拉菜单项识别场景测试工具")
    print("📍 目标坐标: (431, 568)")
    print()
    
    # 多次测试同一坐标
    test_dropdown_recognition_multiple_times(431, 568, 3)
    
    print("\n" + "="*80 + "\n")
    
    # 测试不同坐标
    test_different_coordinates()
    
    print("\n" + "="*80)
    print("💡 结论:")
    print("1. 如果成功率是100%，说明识别逻辑正常工作")
    print("2. 如果成功率较低，可能存在时机或状态问题")
    print("3. 如果不同坐标有不同结果，说明坐标精度很重要")
    print("4. 如果结果不一致，可能存在竞态条件或缓存问题")
