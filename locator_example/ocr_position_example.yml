# OCR和POSITION类型的locator示例文件
# 展示修改后的position字段格式

# OCR类型 - 使用position字段
录制坐标100200:
  type: ocr
  position:
    x: 100
    y: 200

录制坐标350450:
  type: ocr
  position:
    x: 350
    y: 450

# POSITION类型 - 使用position字段
录制坐标500600:
  type: position
  position:
    x: 500
    y: 600

录制坐标800900:
  type: position
  position:
    x: 800
    y: 900

# 对比：UNI类型仍使用原有的datamap格式
示例UNI控件:
  datamap:
    Name: "按钮"
    Rolename: "button"
    ProcessName: "application"
    Coords:
      x: 100
      y: 200
      width: 80
      height: 30
    Key: "示例UNI控件"
