#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试坐标(869, 458)处确定按钮识别问题的脚本
"""

import sys
import os
import time

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts')
if scripts_dir not in sys.path:
    sys.path.insert(0, scripts_dir)

try:
    from UNI import UNI
except ImportError as e:
    print(f"❌ 无法导入UNI模块: {e}")
    print("请确保scripts/UNI.py文件存在")
    sys.exit(1)

def debug_coordinate_recognition(x, y):
    """调试指定坐标的控件识别"""
    print(f"🔍 开始调试坐标({x}, {y})处的控件识别问题")
    print("=" * 80)
    
    # 创建UNI实例
    uni = UNI()
    
    print(f"📍 目标坐标: ({x}, {y})")
    print(f"🖥️  显示服务器类型: {uni.display_server}")
    print()
    
    # 1. 获取活动窗口信息
    print("🔍 步骤1: 获取活动窗口信息")
    try:
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        if active_window:
            print(f"✅ 找到活动窗口:")
            print(f"   窗口名称: {active_window.name}")
            print(f"   进程ID: {processid}")
            print(f"   窗口区域: {activewindow_region}")
            print(f"   窗口角色: {windowRoleName}")
            print(f"   子控件数量: {windowChildCount}")
        else:
            print("❌ 未找到活动窗口")
            return
    except Exception as e:
        print(f"❌ 获取活动窗口失败: {e}")
        return
    
    print()
    
    # 2. 检查是否会被跳过
    print("🔍 步骤2: 检查是否会被跳过控件识别")
    try:
        should_skip = uni._should_skip_control_detection(x, y)
        if should_skip:
            print("⚠️  该坐标会被跳过控件识别（可能导致卡顿）")
            return
        else:
            print("✅ 该坐标不会被跳过")
    except Exception as e:
        print(f"⚠️  检查跳过逻辑失败: {e}")
    
    print()
    
    # 3. 尝试X11层级检测
    print("🔍 步骤3: 尝试X11层级检测")
    try:
        topmost_element = uni._find_topmost_element_at_point(x, y)
        if topmost_element == "SKIP_UNKNOWN_WINDOW":
            print("⚠️  X11层级检测返回跳过Unknown窗口")
            return
        elif topmost_element:
            print(f"✅ X11层级检测找到控件:")
            print(f"   控件名称: {getattr(topmost_element, 'name', 'N/A')}")
            print(f"   控件角色: {getattr(topmost_element, 'getRoleName', lambda: 'N/A')()}")
        else:
            print("❌ X11层级检测未找到控件")
    except Exception as e:
        print(f"❌ X11层级检测失败: {e}")
    
    print()
    
    # 4. 尝试智能深度搜索
    print("🔍 步骤4: 尝试智能深度搜索")
    try:
        found_element = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
        if found_element:
            print(f"✅ 智能深度搜索找到控件:")
            print(f"   控件名称: {getattr(found_element, 'name', 'N/A')}")
            print(f"   控件角色: {getattr(found_element, 'getRoleName', lambda: 'N/A')()}")
            
            # 获取控件详细信息
            try:
                component = found_element.queryComponent()
                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                print(f"   控件位置: x={extents.x}, y={extents.y}")
                print(f"   控件大小: width={extents.width}, height={extents.height}")
                print(f"   控件面积: {extents.width * extents.height}")
                
                # 检查坐标是否在控件范围内
                if (extents.x <= x < extents.x + extents.width and
                    extents.y <= y < extents.y + extents.height):
                    print(f"✅ 目标坐标在控件范围内")
                else:
                    print(f"❌ 目标坐标不在控件范围内")
            except Exception as e:
                print(f"⚠️  获取控件详细信息失败: {e}")
        else:
            print("❌ 智能深度搜索未找到控件")
    except Exception as e:
        print(f"❌ 智能深度搜索失败: {e}")
    
    print()
    
    # 5. 完整的控件识别测试
    print("🔍 步骤5: 完整的控件识别测试")
    try:
        data, text_info = uni.kdk_getElement_Uni(x, y)
        if data and "error" not in data:
            print(f"✅ 完整识别成功:")
            print(f"   控件名称: {data.get('Name', 'N/A')}")
            print(f"   控件角色: {data.get('RoleName', 'N/A')}")
            print(f"   控件坐标: {data.get('Coords', 'N/A')}")
            print(f"   文本信息: {text_info}")
        else:
            print(f"❌ 完整识别失败:")
            print(f"   错误信息: {data.get('error', '未知错误')}")
            print(f"   文本信息: {text_info}")
    except Exception as e:
        print(f"❌ 完整识别异常: {e}")
    
    print()
    print("🔍 调试完成")
    print("=" * 80)

def check_surrounding_area(center_x, center_y, radius=20):
    """检查周围区域的控件识别情况"""
    print(f"🔍 检查坐标({center_x}, {center_y})周围{radius}像素范围内的控件识别情况")
    print("=" * 80)
    
    uni = UNI()
    
    # 检查周围的几个点
    test_points = [
        (center_x, center_y),  # 中心点
        (center_x - radius, center_y),  # 左
        (center_x + radius, center_y),  # 右
        (center_x, center_y - radius),  # 上
        (center_x, center_y + radius),  # 下
        (center_x - radius//2, center_y - radius//2),  # 左上
        (center_x + radius//2, center_y - radius//2),  # 右上
        (center_x - radius//2, center_y + radius//2),  # 左下
        (center_x + radius//2, center_y + radius//2),  # 右下
    ]
    
    for i, (x, y) in enumerate(test_points):
        print(f"📍 测试点{i+1}: ({x}, {y})")
        try:
            data, text_info = uni.kdk_getElement_Uni(x, y)
            if data and "error" not in data:
                print(f"   ✅ 找到控件: {data.get('Name', 'N/A')} ({data.get('RoleName', 'N/A')})")
            else:
                print(f"   ❌ 未找到控件: {data.get('error', '未知错误')}")
        except Exception as e:
            print(f"   ❌ 识别异常: {e}")
        print()

if __name__ == "__main__":
    # 调试目标坐标
    target_x, target_y = 869, 458
    
    print("🚀 确定按钮识别问题调试工具")
    print(f"📍 目标坐标: ({target_x}, {target_y})")
    print()
    
    # 详细调试
    debug_coordinate_recognition(target_x, target_y)
    
    print()
    print("🔍 检查周围区域...")
    check_surrounding_area(target_x, target_y, 20)
    
    print()
    print("💡 调试建议:")
    print("1. 检查窗口是否正确获取")
    print("2. 检查控件是否被跳过逻辑过滤")
    print("3. 检查X11层级检测是否正常工作")
    print("4. 检查智能深度搜索的得分计算")
    print("5. 尝试周围坐标看是否能识别到相同控件")
