#!/bin/bash
# 修复版x86远程显示DEB包构建脚本
# 修复问题：架构不匹配、权限问题、缺少scripts目录
# 基于 build_arm64-deb-with-remote-display.sh 进行修正

set -e

# 默认参数
ARCH="x64"
DEB_ARCH="amd64"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --arch)
      ARCH="$2"
      shift 2
      ;;
    *)
      echo "未知选项: $1"
      echo "用法: $0 [--arch <x64|arm64|armhf>]"
      exit 1
      ;;
  esac
done

# 设置对应的Debian架构
case "$ARCH" in
  "x64") DEB_ARCH="amd64" ;;
  "arm64") DEB_ARCH="arm64" ;;
  "armhf") DEB_ARCH="armhf" ;;
  *)
    echo "错误: 不支持的架构 '$ARCH'"
    exit 1
    ;;
esac

# 获取版本信息
PACKAGE_VERSION=$(node -pe "require('./package.json').version" 2>/dev/null || echo "1.102.0")
BUILD_TIMESTAMP=$(date +%s)

echo "=== 简化版x86远程显示功能 KylinRobot DEB 包构建 ==="
echo "架构: $ARCH ($DEB_ARCH)"
echo "版本: $PACKAGE_VERSION"
echo "时间戳: $BUILD_TIMESTAMP"
echo "特性: 完整node_modules + 架构匹配 + 权限问题 + scripts目录"
echo ""

# 1. 基础环境检查
echo "=== 基础环境检查 ==="
if [ ! -f "package.json" ] || [ ! -f "product.json" ]; then
  echo "错误: 当前目录不是有效的 VSCode 项目根目录"
  exit 1
fi

if [ ! -d "out" ]; then
  echo "错误: out 目录不存在，请先运行编译"
  exit 1
fi

# 远程显示功能检查
REMOTE_DISPLAY_EXT="extensions/remote-display"
if [ ! -d "$REMOTE_DISPLAY_EXT" ]; then
  echo "❌ 远程显示扩展目录不存在: $REMOTE_DISPLAY_EXT"
  exit 1
fi

if [ ! -f "$REMOTE_DISPLAY_EXT/out/extension.js" ]; then
  echo "❌ 远程显示扩展未编译，请先运行: cd $REMOTE_DISPLAY_EXT && npm run compile"
  exit 1
fi

echo "✅ 基础环境检查通过"

# 2. 架构特定的node_modules选择（修复核心问题）
echo "=== 架构特定node_modules选择 ==="

# 🔥 根据目标架构选择正确的node_modules源
SOURCE_NODE_MODULES="node_modules"
BUILD_NODE_MODULES="../VSCode-linux-$ARCH/resources/app/node_modules"

if [ "$ARCH" = "x64" ]; then
  # 🔧 修复：优先使用架构匹配的x86专用node_modules目录
  if [ -d "node_modules_x86" ]; then
    SOURCE_NODE_MODULES="node_modules_x86"
    echo "🔧 使用x86架构专用node_modules: $SOURCE_NODE_MODULES"
  elif [ -d "node_modules_x86_optimized" ]; then
    SOURCE_NODE_MODULES="node_modules_x86_optimized"
    echo "🔧 使用x86架构优化版node_modules: $SOURCE_NODE_MODULES"
  elif [ -d "node_modules_x64" ]; then
    SOURCE_NODE_MODULES="node_modules_x64"
    echo "🔧 使用x64架构专用node_modules: $SOURCE_NODE_MODULES"
  # 检查gulp构建的node_modules是否为正确架构
  elif [ -d "$BUILD_NODE_MODULES" ]; then
    # 验证gulp构建的node_modules架构
    SAMPLE_NODE_FILE=$(find "$BUILD_NODE_MODULES" -name "*.node" -type f | head -1)
    if [ -n "$SAMPLE_NODE_FILE" ]; then
      NODE_ARCH=$(file "$SAMPLE_NODE_FILE" | grep -o "x86-64\|ARM aarch64\|i386" | head -1)
      if [[ "$NODE_ARCH" == *"x86-64"* ]] || [[ "$NODE_ARCH" == *"i386"* ]]; then
        SOURCE_NODE_MODULES="$BUILD_NODE_MODULES"
        echo "🔧 使用gulp构建的x86架构node_modules: $SOURCE_NODE_MODULES"
      else
        echo "⚠️ 警告: gulp构建的node_modules是$NODE_ARCH架构，不匹配x86目标"
        echo "❌ 错误: 未找到x86架构专用node_modules目录"
        echo "请先创建x86架构的node_modules，可能的目录名："
        echo "  - node_modules_x86"
        echo "  - node_modules_x86_optimized"
        echo "  - node_modules_x64"
        exit 1
      fi
    else
      echo "⚠️ 无法验证gulp构建的node_modules架构"
      echo "❌ 错误: 未找到x86架构专用node_modules目录"
      echo "请先创建x86架构的node_modules"
      exit 1
    fi
  else
    echo "❌ 错误: 未找到x86架构专用node_modules目录"
    echo "请先创建x86架构的node_modules，可能的目录名："
    echo "  - node_modules_x86"
    echo "  - node_modules_x86_optimized"
    echo "  - node_modules_x64"
    echo "或者先运行: npm run gulp vscode-linux-x64"
    exit 1
  fi
else
  echo "🔧 使用默认ARM64 node_modules: $SOURCE_NODE_MODULES"
fi

if [ ! -d "$SOURCE_NODE_MODULES" ]; then
  echo "❌ 错误: node_modules目录不存在: $SOURCE_NODE_MODULES"
  exit 1
fi

# 验证架构匹配
echo "🔍 验证node_modules架构匹配..."
SAMPLE_NODE_FILE=$(find "$SOURCE_NODE_MODULES" -name "*.node" -type f | head -1)
if [ -n "$SAMPLE_NODE_FILE" ]; then
  NODE_ARCH=$(file "$SAMPLE_NODE_FILE" | grep -o "x86-64\|ARM aarch64\|i386" | head -1)
  echo "检测到的原生模块架构: $NODE_ARCH"

  if [ "$ARCH" = "x64" ] && [[ "$NODE_ARCH" != *"x86-64"* ]] && [[ "$NODE_ARCH" != *"i386"* ]]; then
    echo "⚠️ 警告: node_modules架构不匹配！期望x86-64，实际: $NODE_ARCH"
    echo "这可能导致应用启动失败"
  elif [ "$ARCH" = "arm64" ] && [[ "$NODE_ARCH" != *"ARM aarch64"* ]]; then
    echo "⚠️ 警告: node_modules架构不匹配！期望ARM aarch64，实际: $NODE_ARCH"
  else
    echo "✅ node_modules架构匹配"
  fi
else
  echo "⚠️ 未找到原生模块文件，无法验证架构"
fi

# 3. 创建架构匹配的node_modules
echo "=== 创建架构匹配的node_modules ==="

# 🔧 简化：不再需要模块列表，直接使用完整的node_modules

# 🔧 简化：直接使用完整的node_modules，不进行文件优化
echo "✅ 使用完整的node_modules，确保功能完整性"
ENHANCED_MODULES="$SOURCE_NODE_MODULES"

# 统计文件数量
original_count=$(find "$SOURCE_NODE_MODULES" -type f | wc -l)
echo "使用完整node_modules，文件数: $original_count"

# 4. 创建应用目录结构
echo "=== 创建应用结构 ==="
APP_DIR="./kylinrobot-ide-$ARCH-fixed"
BUILD_DIR="../VSCode-linux-$ARCH"

rm -rf "$APP_DIR"
mkdir -p "$APP_DIR"

# 基础应用结构构建
if [ -d "$BUILD_DIR" ]; then
  echo "使用已有的构建目录: $BUILD_DIR"
  cp -r "$BUILD_DIR/"* "$APP_DIR/"

  # 如果使用的不是gulp构建的node_modules，则需要替换
  if [[ "$SOURCE_NODE_MODULES" != *"../VSCode-linux-"* ]]; then
    echo "替换为架构匹配的node_modules..."
    rm -rf "$APP_DIR/resources/app/node_modules"
    cp -r "$ENHANCED_MODULES" "$APP_DIR/resources/app/node_modules"
  else
    echo "✅ 使用gulp构建的完整node_modules，无需替换"
  fi

  # 替换为最新编译的扩展
  echo "🔨 更新为最新编译的扩展..."
  if [ -d "extensions" ]; then
    echo "  替换扩展目录为最新编译版本..."
    rm -rf "$APP_DIR/resources/app/extensions"
    cp -r extensions/ "$APP_DIR/resources/app/"
  fi

else
  echo "BUILD_DIR 不存在，手动构建..."

  # 寻找 Electron 分发包
  ELECTRON_DIRS=(
    "node_modules/electron/dist"
    "node_modules/.bin/electron"
    "/usr/share/electron"
  )

  ELECTRON_FOUND=false
  for electron_dir in "${ELECTRON_DIRS[@]}"; do
    if [ -d "$electron_dir" ]; then
      echo "使用 Electron 目录: $electron_dir"
      cp -r "$electron_dir/"* "$APP_DIR/"

      if [ -f "$APP_DIR/electron" ]; then
        mv "$APP_DIR/electron" "$APP_DIR/kylinrobot-ide"
      fi

      ELECTRON_FOUND=true
      break
    fi
  done

  if [ "$ELECTRON_FOUND" = false ]; then
    echo "错误: 未找到 Electron 分发包"
    exit 1
  fi

  # 创建 resources 目录结构
  mkdir -p "$APP_DIR/resources/app"

  # 复制编译后的代码
  echo "复制编译输出..."
  cp -r out/ "$APP_DIR/resources/app/"

  # 使用架构匹配的 node_modules
  echo "使用架构匹配的node_modules..."
  cp -r "$ENHANCED_MODULES" "$APP_DIR/resources/app/node_modules"

  # 复制扩展
  if [ -d "extensions" ]; then
    echo "复制扩展..."
    cp -r extensions/ "$APP_DIR/resources/app/"
  fi

  echo "复制配置文件..."
  cp package.json "$APP_DIR/resources/app/"
  cp product.json "$APP_DIR/resources/app/"

  # 复制其他必要文件
  for file in LICENSE.txt ThirdPartyNotices.txt; do
    if [ -f "$file" ]; then
      cp "$file" "$APP_DIR/resources/app/"
    fi
  done

  if [ -d "resources" ]; then
    echo "复制资源文件..."
    cp -r resources/ "$APP_DIR/resources/app/"
  fi
fi

# 5. 🔥 添加缺失的scripts目录（修复重要问题）
echo "=== 添加scripts目录（修复缺失问题） ==="
if [ -d "scripts" ]; then
  echo "复制scripts目录..."
  mkdir -p "$APP_DIR/resources/app/scripts"
  cp -r scripts/. "$APP_DIR/resources/app/scripts/"
  echo "✅ scripts目录已添加"
else
  echo "⚠️ 警告: scripts目录不存在"
fi

# 6. 🔥 处理 KylinRobot-v2 内置模块（新增功能）
echo "=== 处理 KylinRobot-v2 内置模块 ==="
KYLINROBOT_TAR_SOURCE="KylinRobot-v2.tar.gz"
if [ -f "$KYLINROBOT_TAR_SOURCE" ]; then
  echo "📦 准备 KylinRobot-v2 内置模块..."
  echo "  源压缩包: $KYLINROBOT_TAR_SOURCE"
  echo "  压缩包大小: $(du -h "$KYLINROBOT_TAR_SOURCE" | cut -f1)"

  # 验证压缩包完整性
  if tar -tzf "$KYLINROBOT_TAR_SOURCE" >/dev/null 2>&1; then
    echo "  ✅ 压缩包完整性验证通过"

    # 注意：这里不能直接复制，需要在创建DEB包结构后再复制
    # 标记需要复制KylinRobot-v2压缩包
    KYLINROBOT_READY=true

    echo "  ✅ KylinRobot-v2 内置模块已准备完成"
    echo "     - 源文件: $KYLINROBOT_TAR_SOURCE"
    echo "     - 目标位置: /opt/KylinRobot-v2 (安装时解压)"
    echo "     - 包内路径: /usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
  else
    echo "  ❌ 错误: 压缩包损坏或格式不正确"
    echo "  跳过 KylinRobot-v2 内置模块处理"
  fi
else
  echo "⚠️ 警告: 未找到 KylinRobot-v2.tar.gz，跳过内置模块处理"
  echo "   如需包含内置模块，请确保 KylinRobot-v2.tar.gz 存在于项目根目录"
fi

# 7. 🔥 全面修复权限问题
echo "=== 全面修复权限问题 ==="
chmod +x "$APP_DIR/kylinrobot-ide"

# 修复node_modules权限问题（关键修复）
echo "🔧 修复node_modules权限..."
if [ -d "$APP_DIR/resources/app/node_modules" ]; then
  # 设置所有目录为755，确保普通用户可以读取和执行
  find "$APP_DIR/resources/app/node_modules" -type d -exec chmod 755 {} + 2>/dev/null || true

  # 设置所有普通文件为644，确保普通用户可以读取
  find "$APP_DIR/resources/app/node_modules" -type f -exec chmod 644 {} + 2>/dev/null || true

  # 设置所有.node文件为755，确保普通用户可以执行
  find "$APP_DIR/resources/app/node_modules" -name "*.node" -type f -exec chmod 755 {} + 2>/dev/null || true

  # 设置其他可执行文件为755
  find "$APP_DIR/resources/app/node_modules" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/node_modules" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true

  echo "  ✅ node_modules权限已修复 (目录:755, 文件:644, 可执行文件:755)"
fi

# 修复extensions目录权限
if [ -d "$APP_DIR/resources/app/extensions" ]; then
  echo "🔧 修复extensions权限..."
  find "$APP_DIR/resources/app/extensions" -type d -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -type f -exec chmod 644 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true
  echo "  ✅ extensions权限已修复"
fi

# 修复scripts目录权限（新增）
if [ -d "$APP_DIR/resources/app/scripts" ]; then
  echo "🔧 修复scripts权限..."
  find "$APP_DIR/resources/app/scripts" -type d -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -type f -exec chmod 644 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
  echo "  ✅ scripts权限已修复"
fi

# 确保主程序目录权限正确
find "$APP_DIR/resources/app" -type d -exec chmod 755 {} + 2>/dev/null || true
find "$APP_DIR/resources/app" -maxdepth 1 -type f -exec chmod 644 {} + 2>/dev/null || true

echo "✅ 权限修复完成"

# 8. 创建DEB包
echo "=== 创建修复版DEB包 ==="
DEB_OUTPUT_DIR="./dist"
mkdir -p "$DEB_OUTPUT_DIR"

DEB_FILE="$DEB_OUTPUT_DIR/kylinrobot-ide_${PACKAGE_VERSION}-${BUILD_TIMESTAMP}_${DEB_ARCH}_fixed.deb"

# 创建DEB包结构
DEB_BUILD_DIR="./.build/linux/deb/$DEB_ARCH/fixed"
rm -rf "$DEB_BUILD_DIR"
mkdir -p "$DEB_BUILD_DIR/usr/share/kylinrobot-ide"
mkdir -p "$DEB_BUILD_DIR/usr/bin"
mkdir -p "$DEB_BUILD_DIR/usr/share/applications"
mkdir -p "$DEB_BUILD_DIR/usr/share/pixmaps"
mkdir -p "$DEB_BUILD_DIR/DEBIAN"

# 复制应用到DEB包结构
cp -r "$APP_DIR"/* "$DEB_BUILD_DIR/usr/share/kylinrobot-ide/"

# 复制 KylinRobot-v2 压缩包到DEB包中（如果准备好了）
if [ "$KYLINROBOT_READY" = true ] && [ -f "$KYLINROBOT_TAR_SOURCE" ]; then
  echo "📦 复制 KylinRobot-v2 压缩包到DEB包..."
  cp "$KYLINROBOT_TAR_SOURCE" "$DEB_BUILD_DIR/usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
  echo "  ✅ KylinRobot-v2 压缩包已复制到DEB包结构"
fi

# 创建启动脚本
cat > "$DEB_BUILD_DIR/usr/bin/kylinrobot-ide" << 'EOF'
#!/bin/bash
# KylinRobot IDE 启动脚本

# 检查websockify是否可用
if ! command -v websockify >/dev/null 2>&1; then
    echo "警告: websockify未安装，远程显示功能可能无法正常工作"
    echo "请运行: pip3 install websockify"
fi

# 启动主程序
exec /usr/share/kylinrobot-ide/kylinrobot-ide "$@"
EOF
chmod +x "$DEB_BUILD_DIR/usr/bin/kylinrobot-ide"

# 创建桌面文件
cat > "$DEB_BUILD_DIR/usr/share/applications/kylinrobot-ide.desktop" << EOF
[Desktop Entry]
Name=KylinRobot IDE
Comment=Code Editing. Redefined.
GenericName=Text Editor
Exec=/usr/bin/kylinrobot-ide %U
Icon=kylinrobot-ide
Type=Application
StartupNotify=true
StartupWMClass=kylinrobot-ide
Categories=TextEditor;Development;IDE;
MimeType=text/plain;inode/directory;
Actions=new-empty-window;
Keywords=vscode;code;editor;development;programming;

[Desktop Action new-empty-window]
Name=New Empty Window
Exec=/usr/bin/kylinrobot-ide --new-window %U
Icon=kylinrobot-ide
EOF

# 🎨 创建多尺寸图标支持
echo "🎨 处理应用图标..."

# 创建图标目录结构
mkdir -p "$DEB_BUILD_DIR/usr/share/pixmaps"
mkdir -p "$DEB_BUILD_DIR/usr/share/icons/hicolor/"{16x16,22x22,24x24,32x32,48x48,64x64,128x128,256x256,512x512}"/apps"
mkdir -p "$DEB_BUILD_DIR/usr/share/icons/hicolor/scalable/apps"

# 主图标文件
MAIN_ICON_PNG="resources/linux/kylinrobot-ide.png"
MAIN_ICON_SVG="resources/kylinrobot-logo.svg"
FALLBACK_ICON="resources/linux/code.png"

# 复制主要图标到pixmaps（传统位置）
if [ -f "$MAIN_ICON_PNG" ]; then
  cp "$MAIN_ICON_PNG" "$DEB_BUILD_DIR/usr/share/pixmaps/kylinrobot-ide.png"
  echo "  ✅ 主图标: kylinrobot-ide.png"
elif [ -f "$FALLBACK_ICON" ]; then
  cp "$FALLBACK_ICON" "$DEB_BUILD_DIR/usr/share/pixmaps/kylinrobot-ide.png"
  echo "  ✅ 备用图标: code.png → kylinrobot-ide.png"
else
  echo "  ⚠️ 未找到PNG图标文件"
fi

# 复制SVG图标（可缩放）
if [ -f "$MAIN_ICON_SVG" ]; then
  cp "$MAIN_ICON_SVG" "$DEB_BUILD_DIR/usr/share/icons/hicolor/scalable/apps/kylinrobot-ide.svg"
  echo "  ✅ 可缩放图标: kylinrobot-logo.svg"
fi

# 复制多尺寸PNG图标（如果存在）
ICON_SIZES=(
  "512:resources/server/code-512.png"
  "192:resources/server/code-192.png"
  "128:resources/linux/kylinrobot-ide.png"
  "64:resources/linux/kylinrobot-ide.png"
  "48:resources/linux/kylinrobot-ide.png"
  "32:resources/linux/kylinrobot-ide.png"
)

for size_file in "${ICON_SIZES[@]}"; do
  size=${size_file%:*}
  file=${size_file#*:}

  if [ -f "$file" ]; then
    target_dir="$DEB_BUILD_DIR/usr/share/icons/hicolor/${size}x${size}/apps"
    if [ -d "$target_dir" ]; then
      cp "$file" "$target_dir/kylinrobot-ide.png"
      echo "  ✅ 图标尺寸 ${size}x${size}: $(basename $file)"
    fi
  fi
done

echo "  📊 图标处理完成"

# 创建control文件
cat > "$DEB_BUILD_DIR/DEBIAN/control" << EOF
Package: kylinrobot-ide
Version: $PACKAGE_VERSION-$BUILD_TIMESTAMP
Section: devel
Priority: optional
Architecture: $DEB_ARCH
Depends: libc6, libgtk-3-0, libx11-6, libxss1, libgconf-2-4, libnss3, libatspi2.0-0, libdrm2, libxcomposite1, libxdamage1, libxrandr2, libxfixes3, libxcursor1, libxtst6, libxext6, libxi6, libxrender1, ca-certificates, libgbm1, python3-tk, python3-pil.imagetk, python3-pip, websockify
Recommends: tightvncserver | tigervnc-standalone-server | x11vnc
Maintainer: KylinRobot Team <<EMAIL>>
Homepage: https://kylinrobot.com
Description: KylinRobot IDE with Remote Display
 Code editing redefined for automation and testing.
 This version resolves architecture mismatch, permission issues,
 and includes missing scripts directory.
 .
 Fixes:
 - ✅ Architecture-matched node_modules
 - ✅ Fixed permission issues
 - ✅ Added missing scripts directory
 - ✅ Complete remote display functionality
 .
 Features:
 - VNC remote display support
 - Real-time screenshot capture
 - WebSocket proxy automation
 - Python automation scripts
EOF

# 创建postinst脚本（增强图标处理）
cat > "$DEB_BUILD_DIR/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

echo "🔧 配置 KylinRobot IDE..."

# 🎨 图标缓存更新（全面处理）
echo "🎨 更新图标缓存..."

# 更新传统图标缓存（pixmaps）
if command -v gtk-update-icon-cache >/dev/null 2>&1; then
    echo "  更新pixmaps图标缓存..."
    gtk-update-icon-cache -f /usr/share/pixmaps 2>/dev/null || true
fi

# 更新hicolor图标主题缓存（现代Linux桌面）
if command -v gtk-update-icon-cache >/dev/null 2>&1; then
    echo "  更新hicolor图标主题缓存..."
    gtk-update-icon-cache -f /usr/share/icons/hicolor 2>/dev/null || true
fi

# 更新图标数据库（KDE/Qt应用）
if command -v update-icon-caches >/dev/null 2>&1; then
    echo "  更新KDE图标缓存..."
    update-icon-caches /usr/share/icons/* 2>/dev/null || true
fi

# 🖥️ 桌面环境集成
echo "🖥️ 更新桌面环境集成..."

# 更新桌面数据库（应用程序菜单）
if command -v update-desktop-database >/dev/null 2>&1; then
    echo "  更新桌面数据库..."
    update-desktop-database /usr/share/applications 2>/dev/null || true
fi

# 更新MIME类型数据库
if command -v update-mime-database >/dev/null 2>&1; then
    echo "  更新MIME数据库..."
    update-mime-database /usr/share/mime 2>/dev/null || true
fi

# 🔗 创建符号链接
echo "🔗 创建符号链接..."
if [ ! -L /usr/local/bin/kylinrobot-ide ]; then
    ln -sf /usr/bin/kylinrobot-ide /usr/local/bin/kylinrobot-ide 2>/dev/null || true
    echo "  ✅ 创建全局命令链接: /usr/local/bin/kylinrobot-ide"
fi

# 🔐 确保权限正确
echo "🔐 修复文件权限..."

# 修复node_modules权限
if [ -d /usr/share/kylinrobot-ide/resources/app/node_modules ]; then
    echo "  修复node_modules权限..."
    chmod -R 755 /usr/share/kylinrobot-ide/resources/app/node_modules 2>/dev/null || true
    find /usr/share/kylinrobot-ide/resources/app/node_modules -name "*.node" -exec chmod 755 {} + 2>/dev/null || true
fi

# 修复scripts权限
if [ -d /usr/share/kylinrobot-ide/resources/app/scripts ]; then
    echo "  修复scripts权限..."
    find /usr/share/kylinrobot-ide/resources/app/scripts -name "*.py" -exec chmod 755 {} + 2>/dev/null || true
    find /usr/share/kylinrobot-ide/resources/app/scripts -name "*.sh" -exec chmod 755 {} + 2>/dev/null || true
fi

# 修复主程序权限
echo "  修复主程序权限..."
chmod 755 /usr/share/kylinrobot-ide/kylinrobot-ide 2>/dev/null || true

# 📦 安装 KylinRobot-v2 内置模块到 /opt
echo "📦 安装 KylinRobot-v2 内置模块..."
KYLINROBOT_TAR="/usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
if [ -f "$KYLINROBOT_TAR" ]; then
    echo "  检查压缩包完整性..."
    if tar -tzf "$KYLINROBOT_TAR" >/dev/null 2>&1; then
        echo "  ✅ 压缩包完整性验证通过"

        # 清理现有目录（如果存在）
        if [ -d "/opt/KylinRobot-v2" ]; then
            echo "  清理现有 KylinRobot-v2 目录..."
            rm -rf /opt/KylinRobot-v2
        fi

        # 确保目标目录存在
        mkdir -p /opt

        # 解压到 /opt 目录
        echo "  解压 KylinRobot-v2 到 /opt 目录..."
        cd /opt
        tar -xzf "$KYLINROBOT_TAR"

        # 设置正确的权限
        if [ -d "/opt/KylinRobot-v2" ]; then
            echo "  设置 /opt/KylinRobot-v2 权限..."
            chown -R root:root /opt/KylinRobot-v2
            find /opt/KylinRobot-v2 -type d -exec chmod 755 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -type f -exec chmod 644 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -name "*.py" -exec chmod 755 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -name "*.sh" -exec chmod 755 {} + 2>/dev/null || true
            echo "  ✅ KylinRobot-v2 内置模块安装完成: /opt/KylinRobot-v2"
        else
            echo "  ❌ 警告: KylinRobot-v2 解压失败"
        fi

        # 清理临时文件
        rm -f "$KYLINROBOT_TAR"
    else
        echo "  ❌ 警告: 压缩包损坏，跳过 KylinRobot-v2 安装"
    fi
else
    echo "  ⚠️  警告: 未找到 KylinRobot-v2 压缩包，跳过内置模块安装"
fi

# 🔍 验证图标安装
echo "🔍 验证图标安装..."
icon_count=0

# 检查pixmaps图标
if [ -f /usr/share/pixmaps/kylinrobot-ide.png ]; then
    echo "  ✅ pixmaps图标: /usr/share/pixmaps/kylinrobot-ide.png"
    icon_count=$((icon_count + 1))
fi

# 检查hicolor图标
for size in 16 22 24 32 48 64 128 256 512; do
    if [ -f "/usr/share/icons/hicolor/${size}x${size}/apps/kylinrobot-ide.png" ]; then
        echo "  ✅ hicolor图标 ${size}x${size}: kylinrobot-ide.png"
        icon_count=$((icon_count + 1))
    fi
done

# 检查SVG图标
if [ -f /usr/share/icons/hicolor/scalable/apps/kylinrobot-ide.svg ]; then
    echo "  ✅ 可缩放SVG图标: kylinrobot-ide.svg"
    icon_count=$((icon_count + 1))
fi

echo "  📊 总计安装图标: $icon_count 个"

# 🧹 清理旧的图标缓存（如果存在）
echo "🧹 清理旧缓存..."
rm -f /usr/share/icons/hicolor/icon-theme.cache 2>/dev/null || true

# 🔄 强制重新生成图标缓存
echo "🔄 重新生成图标缓存..."
if command -v gtk-update-icon-cache >/dev/null 2>&1; then
    gtk-update-icon-cache -f -t /usr/share/icons/hicolor 2>/dev/null || true
fi

# 📋 安装完成信息
echo ""
echo "🎉 KylinRobot IDE 安装完成！"
echo ""
echo "📊 修复内容:"
echo "  ✅ 架构匹配的node_modules (x86)"
echo "  ✅ 权限问题已解决"
echo "  ✅ scripts目录已包含"
echo "  ✅ KylinRobot-v2 内置模块 (/opt/KylinRobot-v2)"
echo "  ✅ 多尺寸图标支持"
echo "  ✅ 桌面环境集成"
echo ""
echo "🚀 启动方式:"
echo "  命令行: kylinrobot-ide"
echo "  桌面: 在应用程序菜单中查找 'KylinRobot IDE'"
echo "  图标: 应该在所有桌面环境中正确显示"
echo ""
echo "🔧 如果图标未显示，请运行以下命令："
echo "  sudo gtk-update-icon-cache -f /usr/share/icons/hicolor"
echo "  sudo update-desktop-database"

exit 0
EOF

chmod +x "$DEB_BUILD_DIR/DEBIAN/postinst"

# 创建postrm脚本（图标清理）
cat > "$DEB_BUILD_DIR/DEBIAN/postrm" << 'EOF'
#!/bin/bash
set -e

if [ "$1" = "remove" ] || [ "$1" = "purge" ]; then
    echo "🧹 清理 KylinRobot IDE 图标和配置..."

    # 清理桌面文件
    rm -f /usr/share/applications/kylinrobot-ide.desktop

    # 清理图标文件
    echo "  清理图标文件..."
    rm -f /usr/share/pixmaps/kylinrobot-ide.png

    # 清理hicolor主题图标
    for size in 16 22 24 32 48 64 128 256 512; do
        rm -f "/usr/share/icons/hicolor/${size}x${size}/apps/kylinrobot-ide.png" 2>/dev/null || true
    done
    rm -f /usr/share/icons/hicolor/scalable/apps/kylinrobot-ide.svg

    # 清理符号链接
    rm -f /usr/local/bin/kylinrobot-ide

    # 清理 KylinRobot-v2 内置模块
    echo "  清理 KylinRobot-v2 内置模块..."
    if [ -d "/opt/KylinRobot-v2" ]; then
        rm -rf /opt/KylinRobot-v2
        echo "  ✅ KylinRobot-v2 内置模块已卸载"
    fi

    # 更新图标缓存
    echo "  更新图标缓存..."
    if command -v gtk-update-icon-cache >/dev/null 2>&1; then
        gtk-update-icon-cache -f /usr/share/icons/hicolor 2>/dev/null || true
        gtk-update-icon-cache -f /usr/share/pixmaps 2>/dev/null || true
    fi

    # 更新桌面数据库
    if command -v update-desktop-database >/dev/null 2>&1; then
        update-desktop-database /usr/share/applications 2>/dev/null || true
    fi

    echo "  ✅ KylinRobot IDE 图标和配置已清理"
fi

exit 0
EOF

chmod +x "$DEB_BUILD_DIR/DEBIAN/postrm"

# 构建DEB包
dpkg-deb --build "$DEB_BUILD_DIR" "$DEB_FILE"

if [ -f "$DEB_FILE" ]; then
  # 获取最终统计信息
  FINAL_FILE_COUNT=$(dpkg -c "$DEB_FILE" | wc -l)
  DEB_SIZE=$(du -h "$DEB_FILE" | cut -f1)

  echo ""
  echo "🎯 简化版构建完成！"
  echo "DEB包: $DEB_FILE"
  echo "包大小: $DEB_SIZE"
  echo "文件数量: $FINAL_FILE_COUNT"
  echo ""
  echo "🔧 构建特性:"
  echo "  ✅ 使用完整的架构匹配node_modules ($SOURCE_NODE_MODULES)"
  echo "  ✅ 修复了权限问题 (755/644)"
  echo "  ✅ 添加了缺失的scripts目录"
  echo "  ✅ 包含 KylinRobot-v2 内置模块 (安装到 /opt/KylinRobot-v2)"
  echo "  ✅ 保留完整远程显示功能"
  echo "  ✅ 无文件优化，确保功能完整性"
  echo ""
  echo "📋 安装命令:"
  echo "  sudo dpkg -i '$DEB_FILE'"

  # 验证DEB包内容
  echo ""
  echo "🔍 验证DEB包内容:"

  # 检查scripts目录
  if dpkg --contents "$DEB_FILE" | grep -q "usr/share/kylinrobot-ide/resources/app/scripts"; then
    scripts_files=$(dpkg --contents "$DEB_FILE" | grep "scripts/" | wc -l)
    echo "  ✅ scripts目录已包含 ($scripts_files 个文件)"
  else
    echo "  ❌ scripts目录缺失"
  fi

  # 检查node_modules架构
  node_files=$(dpkg --contents "$DEB_FILE" | grep -c "\.node$" || echo 0)
  echo "  ✅ 原生模块文件数: $node_files"

  # 检查远程显示扩展
  if dpkg --contents "$DEB_FILE" | grep -q "remote-display"; then
    remote_files=$(dpkg --contents "$DEB_FILE" | grep -c "remote-display" || echo 0)
    echo "  ✅ 远程显示扩展文件数: $remote_files"
  else
    echo "  ❌ 远程显示扩展缺失"
  fi

  # 检查 KylinRobot-v2 内置模块
  if dpkg --contents "$DEB_FILE" | grep -q "usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"; then
    kylinrobot_size=$(dpkg --contents "$DEB_FILE" | grep "kylinrobot-v2.tar.gz" | awk '{print $3}')
    echo "  ✅ KylinRobot-v2 内置模块: kylinrobot-v2.tar.gz (${kylinrobot_size} bytes)"
  else
    echo "  ❌ KylinRobot-v2 内置模块缺失"
  fi

  # 🎨 检查图标文件
  echo ""
  echo "🎨 验证图标安装:"

  # 检查pixmaps图标
  if dpkg --contents "$DEB_FILE" | grep -q "usr/share/pixmaps/kylinrobot-ide.png"; then
    echo "  ✅ pixmaps图标: kylinrobot-ide.png"
  else
    echo "  ❌ pixmaps图标缺失"
  fi

  # 检查hicolor图标
  hicolor_count=$(dpkg --contents "$DEB_FILE" | grep "usr/share/icons/hicolor" | grep "kylinrobot-ide" | wc -l)
  echo "  ✅ hicolor图标文件数: $hicolor_count"

  # 检查SVG图标
  if dpkg --contents "$DEB_FILE" | grep -q "scalable/apps/kylinrobot-ide.svg"; then
    echo "  ✅ SVG可缩放图标: kylinrobot-ide.svg"
  else
    echo "  ⚠️ SVG图标缺失（可选）"
  fi

  # 统计总图标数
  total_icons=$(dpkg --contents "$DEB_FILE" | grep -E "(pixmaps|icons).*kylinrobot-ide\.(png|svg)" | wc -l)
  echo "  📊 总计图标文件: $total_icons 个"

else
  echo "❌ DEB包创建失败"
  exit 1
fi

# 清理临时文件
echo "=== 清理临时文件 ==="
rm -rf "$APP_DIR" "$DEB_BUILD_DIR"
# 不再需要清理ENHANCED_MODULES，因为它就是SOURCE_NODE_MODULES

echo ""
echo "🎉 增强版本构建完成！所有问题已解决："
echo "1. ✅ 架构不匹配问题 - 使用正确的x86 node_modules"
echo "2. ✅ 权限问题 - 设置正确的文件权限"
echo "3. ✅ 缺少scripts目录 - 已添加完整scripts目录"
echo "4. ✅ KylinRobot-v2 内置模块 - 自动安装到 /opt/KylinRobot-v2"
echo "5. ✅ 远程显示功能 - 保持完整功能"
echo "6. ✅ 图标处理 - 多尺寸图标支持 + 自动缓存更新"
echo "7. ✅ 完整性保证 - 使用完整node_modules，无文件优化"
echo ""
echo "📦 KylinRobot-v2 内置模块特性:"
echo "  🎯 自动安装到 /opt/KylinRobot-v2"
echo "  🔐 正确的权限设置 (755/644)"
echo "  🧹 卸载时自动清理"
echo "  ✅ 完整性验证和错误处理"
echo ""
echo "🎨 图标特性:"
echo "  📱 多分辨率支持: 16x16 到 512x512"
echo "  🖼️ SVG可缩放图标支持"
echo "  🔄 自动图标缓存更新"
echo "  🖥️ 全桌面环境兼容 (GNOME/KDE/XFCE/等)"
echo "  🧹 卸载时自动清理"
